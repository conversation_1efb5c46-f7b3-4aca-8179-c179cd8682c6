#!/usr/bin/env node

/**
 * Manual verification script for calculation results
 * This script manually verifies the mathematical accuracy of stored calculations
 */

// Sample data from DynamoDB (first 5 records)
const sampleRecords = [
  {
    originalValue: 199.36,
    overdueDays: 294,
    penaltyRate: 2.69,
    dailyRate: 0.0003333,
    storedPenalty: 5.36,
    storedInterest: 19.54,
    storedTotal: 224.26,
    customerName: "<PERSON>"
  },
  {
    originalValue: 1170.22,
    overdueDays: 157,
    penaltyRate: 2.69,
    dailyRate: 0.0003333,
    storedPenalty: 31.48,
    storedInterest: 61.24,
    storedTotal: 1262.94,
    customerName: "<PERSON>"
  },
  {
    originalValue: 1683.93,
    overdueDays: 638,
    penaltyRate: 2.69,
    dailyRate: 0.0003333,
    storedPenalty: 45.30,
    storedInterest: 358.08,
    storedTotal: 2087.31,
    customerName: "<PERSON>"
  },
  {
    originalValue: 1438.88,
    overdueDays: 611,
    penaltyRate: 2.69,
    dailyRate: 0.0003333,
    storedPenalty: 38.71,
    storedInterest: 293.02,
    storedTotal: 1770.61,
    customerName: "<PERSON><PERSON>"
  },
  {
    originalValue: 1573.64,
    overdueDays: 337,
    penaltyRate: 2.69,
    dailyRate: 0.0003333,
    storedPenalty: 42.33,
    storedInterest: 176.75,
    storedTotal: 1792.72,
    customerName: "Dr. Heitor Porto"
  }
];

/**
 * Calculate expected penalty amount using the same logic as the application
 */
function calculateExpectedPenalty(originalValue, penaltyFeeDecimal) {
  if (originalValue == null || penaltyFeeDecimal == null) {
    return 0;
  }

  // Convert to integer arithmetic for precision (work in cents)
  const originalValueCents = Math.round(originalValue * 100);
  
  // Convert decimal rate to integer representation for precise calculations
  const penaltyFeeInteger = Math.round(penaltyFeeDecimal * 10000000);
  
  // Calculate using integer arithmetic
  return Math.round((penaltyFeeInteger * originalValueCents) / 10000000);
}

/**
 * Calculate expected interest charges using the same logic as the application
 */
function calculateExpectedInterest(originalValue, dailyFeeDecimal, overdueDays) {
  if (originalValue == null || dailyFeeDecimal == null || overdueDays == null) {
    return 0;
  }

  // Convert to integer arithmetic for precision (work in cents)
  const originalValueCents = Math.round(originalValue * 100);
  
  // Convert decimal rate to integer representation for precise calculations
  const dailyFeeInteger = Math.round(dailyFeeDecimal * 10000000);
  
  // Calculate using integer arithmetic
  return Math.round((dailyFeeInteger * originalValueCents * overdueDays) / 10000000);
}

/**
 * Calculate expected total amount using the same logic as the application
 */
function calculateExpectedTotal(originalValue, penaltyAmountCents, interestChargesCents) {
  if (originalValue == null) {
    return 0;
  }

  const originalValueCents = Math.round(originalValue * 100);
  return originalValueCents + penaltyAmountCents + interestChargesCents;
}

/**
 * Verify calculations for all sample records
 */
function verifyCalculations() {
  console.log('🔍 Verifying calculation results from DynamoDB...\n');
  
  let totalRecords = sampleRecords.length;
  let validRecords = 0;
  let invalidRecords = [];

  sampleRecords.forEach((record, index) => {
    console.log(`📋 Record ${index + 1}: ${record.customerName}`);
    console.log(`   Original Value: R$ ${record.originalValue.toFixed(2)}`);
    console.log(`   Overdue Days: ${record.overdueDays}`);
    console.log(`   Penalty Rate: ${record.penaltyRate}%`);
    console.log(`   Daily Rate: ${record.dailyRate}`);

    // Calculate expected values using the same logic as the application
    const penaltyFeeDecimal = record.penaltyRate / 100;
    const expectedPenaltyCents = calculateExpectedPenalty(record.originalValue, penaltyFeeDecimal);
    const expectedInterestCents = calculateExpectedInterest(record.originalValue, record.dailyRate, record.overdueDays);
    const expectedTotalCents = calculateExpectedTotal(record.originalValue, expectedPenaltyCents, expectedInterestCents);

    // Convert to decimal for comparison
    const expectedPenalty = expectedPenaltyCents / 100;
    const expectedInterest = expectedInterestCents / 100;
    const expectedTotal = expectedTotalCents / 100;

    // Verify with tolerance of 0.01 (1 cent)
    const tolerance = 0.01;
    const penaltyValid = Math.abs(record.storedPenalty - expectedPenalty) <= tolerance;
    const interestValid = Math.abs(record.storedInterest - expectedInterest) <= tolerance;
    const totalValid = Math.abs(record.storedTotal - expectedTotal) <= tolerance;

    const isValid = penaltyValid && interestValid && totalValid;

    console.log(`   Expected Penalty: R$ ${expectedPenalty.toFixed(2)} | Stored: R$ ${record.storedPenalty.toFixed(2)} | ${penaltyValid ? '✅' : '❌'}`);
    console.log(`   Expected Interest: R$ ${expectedInterest.toFixed(2)} | Stored: R$ ${record.storedInterest.toFixed(2)} | ${interestValid ? '✅' : '❌'}`);
    console.log(`   Expected Total: R$ ${expectedTotal.toFixed(2)} | Stored: R$ ${record.storedTotal.toFixed(2)} | ${totalValid ? '✅' : '❌'}`);
    
    if (isValid) {
      validRecords++;
      console.log(`   ✅ Record is mathematically correct\n`);
    } else {
      invalidRecords.push({
        customerName: record.customerName,
        differences: {
          penalty: Math.abs(record.storedPenalty - expectedPenalty).toFixed(4),
          interest: Math.abs(record.storedInterest - expectedInterest).toFixed(4),
          total: Math.abs(record.storedTotal - expectedTotal).toFixed(4),
        }
      });
      console.log(`   ❌ Record has calculation errors\n`);
    }
  });

  // Final results
  console.log('✅ Verification Complete!\n');
  console.log(`📈 Summary:`);
  console.log(`   Total records verified: ${totalRecords}`);
  console.log(`   Valid calculations: ${validRecords}`);
  console.log(`   Invalid calculations: ${invalidRecords.length}`);
  
  if (totalRecords > 0) {
    const accuracy = ((validRecords / totalRecords) * 100).toFixed(2);
    console.log(`   Accuracy: ${accuracy}%`);
  }

  if (invalidRecords.length > 0) {
    console.log('\n❌ Invalid Calculation Details:');
    invalidRecords.forEach((record, index) => {
      console.log(`${index + 1}. ${record.customerName}`);
      console.log(`   Penalty difference: R$ ${record.differences.penalty}`);
      console.log(`   Interest difference: R$ ${record.differences.interest}`);
      console.log(`   Total difference: R$ ${record.differences.total}`);
    });
  }

  return {
    totalRecords,
    validRecords,
    invalidRecords: invalidRecords.length,
    accuracy: totalRecords > 0 ? (validRecords / totalRecords) * 100 : 0
  };
}

// Run the verification
if (require.main === module) {
  const results = verifyCalculations();
  
  if (results.invalidRecords === 0) {
    console.log('\n🎉 All calculations are mathematically correct!');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some calculations need attention.');
    process.exit(1);
  }
}

module.exports = { verifyCalculations };
