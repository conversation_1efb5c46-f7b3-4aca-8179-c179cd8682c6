#!/usr/bin/env node

/**
 * Simple script to query and display calculation data from DynamoDB table
 * This helps understand what data is currently stored
 */

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, ScanCommand } = require('@aws-sdk/lib-dynamodb');

// Configure AWS DynamoDB client
const clientConfig = {
  region: process.env.AWS_REGION || 'us-east-1',
};

// Add endpoint for local development
if (process.env.DYNAMODB_ENDPOINT) {
  clientConfig.endpoint = process.env.DYNAMODB_ENDPOINT;
  // For LocalStack, we need to set credentials
  clientConfig.credentials = {
    accessKeyId: 'test',
    secretAccessKey: 'test',
  };
}

const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient(clientConfig));

const TABLE_NAME = 'transcendence_portfolio_items_custom_data';

/**
 * Query and display calculation data
 */
async function queryCalculationData() {
  console.log('🔍 Querying calculation data from DynamoDB...\n');

  try {
    let lastEvaluatedKey = null;
    let totalRecords = 0;
    let recordsWithCalculations = 0;
    let sampleRecords = [];

    do {
      const params = {
        TableName: TABLE_NAME,
        Limit: 50, // Small batch for initial exploration
        ExclusiveStartKey: lastEvaluatedKey,
      };

      const result = await dynamoClient.send(new ScanCommand(params));

      if (result.Items) {
        totalRecords += result.Items.length;

        for (const item of result.Items) {
          const customData = item.customData;

          // Check if this record has calculation results
          if (customData.VALOR_TOTAL_CORRIGIDO || customData.VALOR_TOTAL_MULTA || customData.VALOR_TOTAL_ENCARGOS) {
            recordsWithCalculations++;

            // Collect sample records for display
            if (sampleRecords.length < 10) {
              sampleRecords.push({
                id: item.id,
                portfolioItemId: item.portfolioItemId,
                customData: {
                  // Original values (handle both old and new property names)
                  VALOR_DIVIDA_ORIGINAL: customData.VALOR_DIVIDA_ORIGINAL || customData.VALOR_ORIGINAL_DA_DIVIDA,
                  DIAS_ATRASO: customData.DIAS_ATRASO || customData.DIAS_EM_ATRASO,
                  PERCENTUAL_MULTA: customData.PERCENTUAL_MULTA,
                  PERCENTUAL_JUROS_DIARIOS: customData.PERCENTUAL_JUROS_DIARIOS,
                  // Calculated values
                  VALOR_TOTAL_MULTA: customData.VALOR_TOTAL_MULTA,
                  VALOR_TOTAL_ENCARGOS: customData.VALOR_TOTAL_ENCARGOS,
                  VALOR_TOTAL_CORRIGIDO: customData.VALOR_TOTAL_CORRIGIDO,
                }
              });
            }
          }
        }
      }

      lastEvaluatedKey = result.LastEvaluatedKey;

      // Progress indicator
      process.stdout.write(`\r📊 Scanned ${totalRecords} records...`);

      // Stop after first batch for initial exploration
      break;

    } while (lastEvaluatedKey);

    // Display results
    console.log('\n\n📈 Query Results:\n');
    console.log(`Total records scanned: ${totalRecords}`);
    console.log(`Records with calculations: ${recordsWithCalculations}`);

    if (sampleRecords.length > 0) {
      console.log('\n📋 Sample Records with Calculations:\n');

      sampleRecords.forEach((record, index) => {
        console.log(`${index + 1}. Record ID: ${record.id}`);
        console.log(`   Portfolio Item ID: ${record.portfolioItemId}`);
        console.log(`   Original Debt: ${record.customData.VALOR_DIVIDA_ORIGINAL || 'N/A'}`);
        console.log(`   Overdue Days: ${record.customData.DIAS_ATRASO || 'N/A'}`);
        console.log(`   Penalty Rate: ${record.customData.PERCENTUAL_MULTA || 'N/A'}%`);
        console.log(`   Daily Rate: ${record.customData.PERCENTUAL_JUROS_DIARIOS || 'N/A'}`);
        console.log(`   Calculated Penalty: R$ ${record.customData.VALOR_TOTAL_MULTA || 'N/A'}`);
        console.log(`   Calculated Interest: R$ ${record.customData.VALOR_TOTAL_ENCARGOS || 'N/A'}`);
        console.log(`   Total Corrected: R$ ${record.customData.VALOR_TOTAL_CORRIGIDO || 'N/A'}`);
        console.log('   ---');
      });
    } else {
      console.log('\n⚠️  No records with calculation results found in the scanned batch.');
      console.log('   This could mean:');
      console.log('   - No calculations have been performed yet');
      console.log('   - Calculations are stored in a different format');
      console.log('   - The table is empty or has different data structure');
    }

    // Show available property names in the first few records
    if (totalRecords > 0) {
      console.log('\n🔍 Available Properties in Sample Records:');
      const firstItem = await getFirstRecord();
      if (firstItem && firstItem.customData) {
        const properties = Object.keys(firstItem.customData).sort();
        console.log(`   ${properties.join(', ')}`);
      }
    }

    return {
      totalRecords,
      recordsWithCalculations,
      sampleRecords
    };

  } catch (error) {
    console.error('❌ Error during query:', error);
    throw error;
  }
}

/**
 * Get the first record to examine structure
 */
async function getFirstRecord() {
  try {
    const params = {
      TableName: TABLE_NAME,
      Limit: 1,
    };

    const result = await dynamoClient.send(new ScanCommand(params));
    return result.Items && result.Items.length > 0 ? result.Items[0] : null;
  } catch (error) {
    console.error('Error getting first record:', error);
    return null;
  }
}

// Run the query if this script is executed directly
if (require.main === module) {
  queryCalculationData()
    .then((results) => {
      console.log('\n✅ Query completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Query failed:', error);
      process.exit(1);
    });
}

module.exports = { queryCalculationData };
