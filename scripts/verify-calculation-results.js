#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify calculation results stored in DynamoDB table transcendence_portfolio_items_custom_data
 * This script checks if the VALOR_TOTAL_CORRIGIDO property contains mathematically correct values
 */

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, ScanCommand } = require('@aws-sdk/lib-dynamodb');

// Configure AWS DynamoDB client
const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({
  region: process.env.AWS_REGION || 'us-east-1',
  endpoint: process.env.DYNAMODB_ENDPOINT || undefined, // For local development
}));

const TABLE_NAME = 'transcendence_portfolio_items_custom_data';

/**
 * Calculate expected penalty amount using the same logic as the application
 */
function calculateExpectedPenalty(originalValue, penaltyFeeDecimal) {
  if (originalValue == null || penaltyFeeDecimal == null) {
    return 0;
  }

  // Convert to integer arithmetic for precision (work in cents)
  const originalValueCents = Math.round(originalValue * 100);
  
  // Convert decimal rate to integer representation for precise calculations
  const penaltyFeeInteger = Math.round(penaltyFeeDecimal * 10000000);
  
  // Calculate using integer arithmetic
  return Math.round((penaltyFeeInteger * originalValueCents) / 10000000);
}

/**
 * Calculate expected interest charges using the same logic as the application
 */
function calculateExpectedInterest(originalValue, dailyFeeDecimal, overdueDays) {
  if (originalValue == null || dailyFeeDecimal == null || overdueDays == null) {
    return 0;
  }

  // Convert to integer arithmetic for precision (work in cents)
  const originalValueCents = Math.round(originalValue * 100);
  
  // Convert decimal rate to integer representation for precise calculations
  const dailyFeeInteger = Math.round(dailyFeeDecimal * 10000000);
  
  // Calculate using integer arithmetic
  return Math.round((dailyFeeInteger * originalValueCents * overdueDays) / 10000000);
}

/**
 * Calculate expected total amount using the same logic as the application
 */
function calculateExpectedTotal(originalValue, penaltyAmountCents, interestChargesCents) {
  if (originalValue == null) {
    return 0;
  }

  const originalValueCents = Math.round(originalValue * 100);
  return originalValueCents + penaltyAmountCents + interestChargesCents;
}

/**
 * Verify a single record's calculation results
 */
function verifyRecord(record) {
  const customData = record.customData;
  
  // Check if this record has calculation results
  if (!customData.VALOR_TOTAL_CORRIGIDO) {
    return { valid: true, reason: 'No calculation results to verify' };
  }

  // Extract values (handle both old and new property names)
  const originalValue = parseFloat((customData.VALOR_DIVIDA_ORIGINAL || customData.VALOR_ORIGINAL_DA_DIVIDA || '0').toString().replace(',', '.'));
  const overdueDays = parseInt((customData.DIAS_ATRASO || customData.DIAS_EM_ATRASO || '0').toString());
  const penaltyFeeDecimal = (customData.PERCENTUAL_MULTA || 0) / 100;
  const dailyFeeDecimal = customData.PERCENTUAL_JUROS_DIARIOS || 0;

  // Skip if essential data is missing
  if (isNaN(originalValue) || isNaN(overdueDays)) {
    return { valid: true, reason: 'Missing essential calculation data' };
  }

  // Calculate expected values
  const expectedPenaltyCents = calculateExpectedPenalty(originalValue, penaltyFeeDecimal);
  const expectedInterestCents = calculateExpectedInterest(originalValue, dailyFeeDecimal, overdueDays);
  const expectedTotalCents = calculateExpectedTotal(originalValue, expectedPenaltyCents, expectedInterestCents);

  // Convert to decimal for comparison
  const expectedPenalty = expectedPenaltyCents / 100;
  const expectedInterest = expectedInterestCents / 100;
  const expectedTotal = expectedTotalCents / 100;

  // Get stored values
  const storedPenalty = parseFloat(customData.VALOR_TOTAL_MULTA || '0');
  const storedInterest = parseFloat(customData.VALOR_TOTAL_ENCARGOS || '0');
  const storedTotal = parseFloat(customData.VALOR_TOTAL_CORRIGIDO || '0');

  // Verify with tolerance of 0.01 (1 cent)
  const tolerance = 0.01;
  const penaltyValid = Math.abs(storedPenalty - expectedPenalty) <= tolerance;
  const interestValid = Math.abs(storedInterest - expectedInterest) <= tolerance;
  const totalValid = Math.abs(storedTotal - expectedTotal) <= tolerance;

  const isValid = penaltyValid && interestValid && totalValid;

  return {
    valid: isValid,
    details: {
      originalValue,
      overdueDays,
      penaltyFeeDecimal,
      dailyFeeDecimal,
      expected: {
        penalty: expectedPenalty.toFixed(2),
        interest: expectedInterest.toFixed(2),
        total: expectedTotal.toFixed(2),
      },
      stored: {
        penalty: storedPenalty.toFixed(2),
        interest: storedInterest.toFixed(2),
        total: storedTotal.toFixed(2),
      },
      differences: {
        penalty: Math.abs(storedPenalty - expectedPenalty).toFixed(4),
        interest: Math.abs(storedInterest - expectedInterest).toFixed(4),
        total: Math.abs(storedTotal - expectedTotal).toFixed(4),
      },
      valid: {
        penalty: penaltyValid,
        interest: interestValid,
        total: totalValid,
      }
    }
  };
}

/**
 * Main verification function
 */
async function verifyCalculationResults() {
  console.log('🔍 Starting verification of calculation results in DynamoDB...\n');

  try {
    let lastEvaluatedKey = null;
    let totalRecords = 0;
    let recordsWithCalculations = 0;
    let validRecords = 0;
    let invalidRecords = [];

    do {
      const params = {
        TableName: TABLE_NAME,
        Limit: 100, // Process in batches
        ExclusiveStartKey: lastEvaluatedKey,
      };

      const result = await dynamoClient.send(new ScanCommand(params));
      
      if (result.Items) {
        totalRecords += result.Items.length;

        for (const item of result.Items) {
          const verification = verifyRecord(item);
          
          if (item.customData.VALOR_TOTAL_CORRIGIDO) {
            recordsWithCalculations++;
            
            if (verification.valid) {
              validRecords++;
            } else {
              invalidRecords.push({
                id: item.id,
                portfolioItemId: item.portfolioItemId,
                verification
              });
            }
          }
        }
      }

      lastEvaluatedKey = result.LastEvaluatedKey;
      
      // Progress indicator
      process.stdout.write(`\r📊 Processed ${totalRecords} records...`);
      
    } while (lastEvaluatedKey);

    // Final results
    console.log('\n\n✅ Verification Complete!\n');
    console.log(`📈 Summary:`);
    console.log(`   Total records scanned: ${totalRecords}`);
    console.log(`   Records with calculations: ${recordsWithCalculations}`);
    console.log(`   Valid calculations: ${validRecords}`);
    console.log(`   Invalid calculations: ${invalidRecords.length}`);
    
    if (recordsWithCalculations > 0) {
      const accuracy = ((validRecords / recordsWithCalculations) * 100).toFixed(2);
      console.log(`   Accuracy: ${accuracy}%`);
    }

    // Show details of invalid records
    if (invalidRecords.length > 0) {
      console.log('\n❌ Invalid Calculation Details:');
      invalidRecords.slice(0, 5).forEach((record, index) => {
        console.log(`\n${index + 1}. Record ID: ${record.id}`);
        console.log(`   Portfolio Item ID: ${record.portfolioItemId}`);
        console.log(`   Original Value: R$ ${record.verification.details.originalValue}`);
        console.log(`   Overdue Days: ${record.verification.details.overdueDays}`);
        console.log(`   Expected Total: R$ ${record.verification.details.expected.total}`);
        console.log(`   Stored Total: R$ ${record.verification.details.stored.total}`);
        console.log(`   Difference: R$ ${record.verification.details.differences.total}`);
      });
      
      if (invalidRecords.length > 5) {
        console.log(`\n   ... and ${invalidRecords.length - 5} more invalid records`);
      }
    }

    return {
      totalRecords,
      recordsWithCalculations,
      validRecords,
      invalidRecords: invalidRecords.length,
      accuracy: recordsWithCalculations > 0 ? (validRecords / recordsWithCalculations) * 100 : 0
    };

  } catch (error) {
    console.error('❌ Error during verification:', error);
    throw error;
  }
}

// Run the verification if this script is executed directly
if (require.main === module) {
  verifyCalculationResults()
    .then((results) => {
      if (results.invalidRecords === 0) {
        console.log('\n🎉 All calculations are mathematically correct!');
        process.exit(0);
      } else {
        console.log('\n⚠️  Some calculations need attention.');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 Verification failed:', error);
      process.exit(1);
    });
}

module.exports = { verifyCalculationResults };
