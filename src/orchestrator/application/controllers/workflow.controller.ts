import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  StreamableFile,
  Version,
} from '@nestjs/common';
import { WorkflowUseCase } from '@orchestrator/application/use-cases/workflow.use-case';
import { ExecuteWorkflowRequestDto } from '@orchestrator/application/dto/in/execute-workflow-request.dto';
import { SaveWorkflowRequestDto } from '@orchestrator/application/dto/in/save-workflow-request.dto';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('orchestrator/workflows')
export class WorkflowController {
  constructor(private readonly workflowUseCase: WorkflowUseCase) {}

  @Get('health')
  @Version('1')
  async health(): Promise<string> {
    return 'OK';
  }

  @Post()
  @Version('1')
  async createWorkflow(@Body() createWorkflowRequestDto: SaveWorkflowRequestDto): Promise<any> {
    const CreateWorkflowResponseDto = await this.workflowUseCase.saveWorkflow(
      createWorkflowRequestDto,
    );
    return {
      statusCode: 201,
      data: CreateWorkflowResponseDto,
    };
  }

  @Put('/:workflowId')
  @Version('1')
  async updateWorkflow(
    @Param('workflowId') workflowId: string,
    @Body() saveWorkflowRequestDto: SaveWorkflowRequestDto,
  ): Promise<any> {
    saveWorkflowRequestDto.id = workflowId;
    const workflowResponseDto = await this.workflowUseCase.saveWorkflow(saveWorkflowRequestDto);
    return {
      statusCode: 200,
      data: workflowResponseDto,
    };
  }

  @Get('/:workflowId')
  @Version('1')
  async getWorkflowDetails(@Param('workflowId') workflowId: string): Promise<any> {
    const workflowResponseDto = await this.workflowUseCase.getWorkflow(workflowId);
    return {
      statusCode: 201,
      data: workflowResponseDto,
    };
  }

  @ApiOperation({ summary: 'Get variables for a workflow' })
  @ApiResponse({
    status: 200,
    description: 'Variables for a workflow',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        data: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  @ApiParam({
    name: 'workflowId',
    description: 'Workflow ID',
    type: String,
    required: true,
    example: '43e3ceed-58da-4ff2-bddf-67cb79d4433f',
  })
  @Get('/:workflowId/variables')
  @Version('1')
  async getWorkflowVariables(@Param('workflowId') workflowId: string): Promise<any> {
    const variables = await this.workflowUseCase.getWorkflowVariables(workflowId);
    return {
      statusCode: 200,
      data: variables,
    };
  }

  @ApiOperation({ summary: 'Download template csv for a workflow' })
  @ApiResponse({
    status: 200,
    description: 'Template csv for a workflow',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        file: { type: 'stream' },
      },
    },
  })
  @Get('/:workflowId/template-csv')
  @Version('1')
  async downloadTemplateCsv(@Param('workflowId') workflowId: string): Promise<StreamableFile> {
    const templateCsv = await this.workflowUseCase.getWorkflowTemplateCsv(workflowId);
    return new StreamableFile(templateCsv, {
      type: 'text/csv',
      disposition: 'attachment; filename="template.csv"',
    });
  }

  @Delete('/:workflowId')
  @Version('1')
  async delete(@Param('workflowId') workflowId: string): Promise<any> {
    await this.workflowUseCase.delete(workflowId);
    return {
      statusCode: 200,
    };
  }

  @Post('start')
  @Version('1')
  async startWorkflow(@Body() body: any) {
    const result = await this.workflowUseCase.startWorkflow(body.workflowId);
    return {
      statusCode: 201,
      data: result,
    };
  }

  @Post('execute')
  @Version('1')
  async execute(@Body() executeWorkflowRequestDto: ExecuteWorkflowRequestDto) {
    const result = await this.workflowUseCase.execute(executeWorkflowRequestDto);
    return {
      statusCode: 201,
      data: result,
    };
  }

  @Post('execute-next-step')
  @Version('1')
  async executeNextStep(@Body() executeWorkflowRequestDto: ExecuteWorkflowRequestDto) {
    const result = await this.workflowUseCase.executeNext(executeWorkflowRequestDto);
    return {
      statusCode: 201,
      data: result,
    };
  }

  @Post('execute-previous-step')
  @Version('1')
  async executePreviousStep(@Body() executeWorkflowRequestDto: ExecuteWorkflowRequestDto) {
    const result = await this.workflowUseCase.executeBack(executeWorkflowRequestDto);
    return {
      statusCode: 201,
      data: result,
    };
  }

  @Post('restart-current-step/:workflowExecutionId')
  @Version('1')
  async restartCurrentStep(
    @Param('workflowExecutionId') workflowExecutionId: string,
  ): Promise<any> {
    const result = await this.workflowUseCase.restartCurrentStep(workflowExecutionId);
    return {
      statusCode: 201,
      data: result,
    };
  }
}
