import { HttpStatus, Inject, Injectable } from '@nestjs/common';

import { WorkflowPort } from '@orchestrator/infrastructure/ports/db/workflow.port';
import { randomUUID as uuidv4 } from 'crypto';

import { WorkFlowExecutionPort } from '@orchestrator/infrastructure/ports/db/workflow-execution.port';
import { ExecuteWorkflowRequestDto } from '@orchestrator/application/dto/in/execute-workflow-request.dto';
import {
  StepExecution,
  WorkflowExecution,
} from '@orchestrator/domain/entities/workflow-execution.entity';

import { TaskPort } from '@orchestrator/infrastructure/ports/http/task.port';
import { ExecuteTask } from '@orchestrator/misc/interfaces/in/execute-task';
import { SaveWorkflowRequestDto } from '@orchestrator/application/dto/in/save-workflow-request.dto';
import { Middleware, Step, Workflow } from '@orchestrator/domain/entities/workflow.entity';
import {
  MiddlewareResponseDto,
  StepWorkflowResponseDto,
  WorkflowResponseDto,
} from '@orchestrator/application/dto/out/create-workflow-response.dto';
import { StartWorkflowResponseDto } from '@orchestrator/application/dto/out/start-workflow-response.dto';
import { ExecuteWorkflowResponseDto } from '@orchestrator/application/dto/out/execute-workflow-response.dto';
import {
  MiddlewareToolCategory,
  MiddlewareType,
  RecordStatus,
  StepExecutionStatus,
} from '@common/enums';
import { ExecuteSimplifiedTask } from '@orchestrator/misc/interfaces/in/execute-simplified-task';
import { logger } from '@edutalent/commons-sdk';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { IntegrationToolFactory } from '@orchestrator/tool/integrations/services/integration-tool-factory';
import {
  IntegrationException,
  IntegrationExceptionStatus,
} from '@common/exception/types/IntegrationException';
import { WorkflowExecutionException } from '@common/exception/types/WorkflowExecutionException';
import { AgentPort } from '@orchestrator/infrastructure/ports/http/agent.port';
import { Readable } from 'stream';

@Injectable()
export class WorkflowUseCase {
  constructor(
    @Inject('WorkflowPort')
    private readonly workFlowAdapter: WorkflowPort,
    @Inject('WorkFlowExecutionPort')
    private readonly workFlowExecutionAdapter: WorkFlowExecutionPort,
    @Inject('TaskPort')
    private readonly TaskAdapter: TaskPort,
    @Inject()
    private readonly integrationFactory: IntegrationToolFactory,
    @Inject('AgentPort')
    private readonly agentAdapter: AgentPort,
  ) {}

  async saveWorkflow(saveWorkflowRequestDto: SaveWorkflowRequestDto): Promise<WorkflowResponseDto> {
    logger.info('Creating workflow: ', saveWorkflowRequestDto);
    const steps = saveWorkflowRequestDto.steps.map(step => {
      const middlewares = step.middlewares?.map(middleware => {
        return new Middleware(
          uuidv4(),
          middleware.name,
          middleware.description,
          middleware.taskId,
          middleware.params,
          middleware.type,
          middleware.showOff,
          middleware.category,
        );
      });

      return new Step(
        uuidv4(),
        step.description,
        step.order,
        step.taskId,
        step.params,
        middlewares,
      );
    });

    const workflowId = saveWorkflowRequestDto.id || uuidv4();
    const workflow = new Workflow(
      workflowId,
      saveWorkflowRequestDto.name,
      saveWorkflowRequestDto.description,
      RecordStatus.ACTIVE,
      steps,
    );

    await this.workFlowAdapter.save(workflow);

    const stepsWorkflow = steps.map(step => {
      const middlewares = step.middlewares.map(middleware => {
        return new MiddlewareResponseDto(
          middleware.id,
          middleware.name,
          middleware.description,
          middleware.taskId,
          middleware.params,
          middleware.type,
          middleware.showOff,
          middleware.category,
        );
      });
      return new StepWorkflowResponseDto(
        step.id,
        step.description,
        step.taskId,
        step.order,
        middlewares,
      );
    });

    return new WorkflowResponseDto(
      workflow.id,
      workflow.name,
      workflow.description,
      workflow.status,
      stepsWorkflow,
    );
  }

  async getWorkflow(workflowId: string): Promise<WorkflowResponseDto> {
    logger.info('Getting workflow: ', workflowId);
    const workflow = await this.workFlowAdapter.get(workflowId);

    const stepsWorkflow = workflow.steps.map(step => {
      const middlewares = step.middlewares?.map(middleware => {
        return new MiddlewareResponseDto(
          middleware.id,
          middleware.name,
          middleware.description,
          middleware.taskId,
          middleware.params,
          middleware.type,
          middleware.showOff,
        );
      });

      return new StepWorkflowResponseDto(
        step.id,
        step.description,
        step.taskId,
        step.order,
        middlewares,
      );
    });

    return new WorkflowResponseDto(
      workflow.id,
      workflow.name,
      workflow.description,
      workflow.status,
      stepsWorkflow,
    );
  }

  async getWorkflowVariables(workflowId: string): Promise<string[]> {
    logger.info('Getting workflow variables: ', workflowId);
    const workflow = await this.workFlowAdapter.get(workflowId);
    const variables = [];

    //Get workflow step.order = 1 only
    const firstStep = workflow.steps.filter(step => step.order === 1)[0];
    const tasksAndAgentsBackstoryVariables =
      await this.TaskAdapter.getTasksAndAgentsBackstoryVariables(firstStep.taskId);
    variables.push(...tasksAndAgentsBackstoryVariables);

    return [...new Set(variables)];
  }

  async getWorkflowTemplateCsv(workflowId: string): Promise<Readable> {
    logger.info('Getting workflow template csv: ', workflowId);
    const variables = await this.getWorkflowVariables(workflowId);
    const templateCsv = variables.join(',');
    const stream = new Readable();
    stream.push(templateCsv);
    stream.push(null);

    return stream;
  }

  async delete(workflowId: string): Promise<void> {
    logger.info('Deleting workflow: ', workflowId);
    const workflow = await this.workFlowAdapter.get(workflowId);

    if (!workflow) {
      throw new Error(`Workflow with id ${workflowId} not found`);
    }

    await this.workFlowAdapter.delete(workflow);
  }

  async startWorkflow(workflowId: string): Promise<StartWorkflowResponseDto> {
    logger.info('Starting workflow: ', workflowId);
    const workflow = await this.workFlowAdapter.get(workflowId);
    const stepFirst = workflow.steps.filter(step => step.order === 1)[0];

    const stepExecution = new StepExecution(
      uuidv4(),
      stepFirst.id,
      stepFirst.order,
      StepExecutionStatus.RUNNING,
    );

    const workflowExecution = new WorkflowExecution(uuidv4(), workflowId, stepExecution.id, [
      stepExecution,
    ]);

    await this.workFlowExecutionAdapter.save(workflowExecution);

    return new StartWorkflowResponseDto(workflowExecution.id, stepExecution.id, stepFirst.order);
  }

  async execute(
    executeWorkflowRequestDto: ExecuteWorkflowRequestDto,
  ): Promise<ExecuteWorkflowResponseDto> {
    logger.info(
      'Workflow Use Case - Execute - Executing workflow: ' +
        JSON.stringify(executeWorkflowRequestDto),
    );

    const workflowExecution = await this.workFlowExecutionAdapter.getById(
      executeWorkflowRequestDto.workflowExecutionId,
    );
    const currentStepExecution = workflowExecution.stepExecutions.filter(
      stepExecution => stepExecution.id === workflowExecution.currentStepExecutionId,
    )[0];

    const workflow = await this.workFlowAdapter.get(workflowExecution.workflowId);
    const stepWorkflow = workflow.steps.filter(step => step.id === currentStepExecution.stepId)[0];

    let combinedParams = {
      ...executeWorkflowRequestDto.params,
      ...stepWorkflow.params,
    };

    if (currentStepExecution.status === StepExecutionStatus.STARTED && stepWorkflow.middlewares) {
      const combinedMiddlewareResponse = await this.executePostStartMiddlewares(
        stepWorkflow,
        combinedParams,
      );

      combinedParams = {
        ...combinedParams,
        ...combinedMiddlewareResponse,
      };

      currentStepExecution.status = StepExecutionStatus.RUNNING;
    }

    const taskResult = await this.TaskAdapter.executeTask(
      new ExecuteTask(
        stepWorkflow.taskId,
        currentStepExecution.id,
        executeWorkflowRequestDto.messageType,
        combinedParams,
        executeWorkflowRequestDto.fileUrl,
        executeWorkflowRequestDto.lang,
      ),
    );

    logger.info(
      `Task result for workflow execution id: ${workflowExecution.id}, step execution id: ${
        currentStepExecution.id
      }: ${JSON.stringify(taskResult)}`,
    );

    workflowExecution.stepExecutions = workflowExecution.stepExecutions.map(stepExecution => {
      return stepExecution.id === currentStepExecution.id ? currentStepExecution : stepExecution;
    });

    await this.workFlowExecutionAdapter.update(workflowExecution);

    let combinedMiddlewareResponse: Record<string, any> = {};

    if (stepWorkflow.middlewares && stepWorkflow.middlewares.length > 0) {
      combinedMiddlewareResponse = await this.executePostMiddlewares(
        stepWorkflow,
        currentStepExecution,
      );
    }

    if (
      combinedMiddlewareResponse['routing-middleware'] &&
      combinedMiddlewareResponse['routing-middleware']['go_to_step']
    ) {
      const routingResponse = combinedMiddlewareResponse['routing-middleware'];
      logger.info(
        `Workflow Use Case - Execute - Workflow execution ${
          workflowExecution.id
        } routing response ${JSON.stringify(routingResponse)}`,
      );

      const goToStepValue = routingResponse['go_to_step'];

      if (goToStepValue) {
        const stepOrder = parseInt(goToStepValue, 10);
        return this.startStepByOrder(executeWorkflowRequestDto, stepOrder);
      }
    }

    return new Promise(resolve => {
      resolve(
        new ExecuteWorkflowResponseDto(
          workflowExecution.id,
          currentStepExecution.id,
          currentStepExecution.order,
          taskResult.output,
          taskResult.outputType,
          combinedMiddlewareResponse,
        ),
      );
    });
  }

  async executeNext(executeWorkflowRequestDto: ExecuteWorkflowRequestDto): Promise<any> {
    logger.info('Executing next step: ', executeWorkflowRequestDto);

    const workflowExecution = await this.workFlowExecutionAdapter.getById(
      executeWorkflowRequestDto.workflowExecutionId,
    );
    logger.info('Workflow execution: ', workflowExecution);

    const currentStepExecution = workflowExecution.stepExecutions.filter(
      stepExecution => stepExecution.id === workflowExecution.currentStepExecutionId,
    )[0];
    logger.info('Current step execution: ', currentStepExecution);

    const workflow = await this.workFlowAdapter.get(workflowExecution.workflowId);
    logger.info('Workflow: ', workflow);

    const nextStepWorkflow = workflow.steps.filter(
      step => step.order === currentStepExecution.order + 1,
    )?.[0];

    if (!nextStepWorkflow) {
      throw new BusinessException(
        'Workflow',
        'Next step not found',
        BusinessExceptionStatus.ITEM_NOT_FOUND,
        'NextStepError',
      );
    }

    const nextStepExecution = new StepExecution(
      uuidv4(),
      nextStepWorkflow.id,
      nextStepWorkflow.order,
      StepExecutionStatus.RUNNING,
    );
    workflowExecution.currentStepExecutionId = nextStepExecution.id;
    workflowExecution.stepExecutions.push(nextStepExecution);

    currentStepExecution.status = StepExecutionStatus.FINISHED;
    workflowExecution.stepExecutions = workflowExecution.stepExecutions.map(stepExecution => {
      return stepExecution.id === currentStepExecution.id ? currentStepExecution : stepExecution;
    });

    logger.info('Workflow execution updated: ', workflowExecution);
    await this.workFlowExecutionAdapter.save(workflowExecution);

    const combinedParams = {
      ...nextStepWorkflow.params,
      ...executeWorkflowRequestDto.params,
    };

    logger.info('Executing next step task with params: ', combinedParams);
    const taskResult = await this.TaskAdapter.executeTask(
      new ExecuteTask(
        nextStepWorkflow.taskId,
        nextStepExecution.id,
        executeWorkflowRequestDto.messageType,
        combinedParams,
        executeWorkflowRequestDto.fileUrl,
        executeWorkflowRequestDto.lang,
      ),
    );

    logger.info('Task result: ', taskResult);

    return new Promise(resolve => {
      resolve(taskResult);
    });
  }

  async executeBack(executeWorkflowRequestDto: ExecuteWorkflowRequestDto): Promise<any> {
    logger.info('Executing back step: ', executeWorkflowRequestDto);
    const workflowExecution = await this.workFlowExecutionAdapter.getById(
      executeWorkflowRequestDto.workflowExecutionId,
    );
    const currentStepExecution = workflowExecution.stepExecutions.filter(
      stepExecution => stepExecution.id === workflowExecution.currentStepExecutionId,
    )[0];
    logger.info('Current step execution: ', currentStepExecution);

    const workflow = await this.workFlowAdapter.get(workflowExecution.workflowId);
    const previousStepWorkflow = workflow.steps.filter(
      step => step.order === currentStepExecution.order - 1,
    )[0];

    const previousStepExecution = new StepExecution(
      uuidv4(),
      previousStepWorkflow.id,
      previousStepWorkflow.order,
      StepExecutionStatus.RUNNING,
    );

    workflowExecution.currentStepExecutionId = previousStepExecution.id;

    workflowExecution.stepExecutions.push(previousStepExecution);

    currentStepExecution.status = StepExecutionStatus.CANCELLED;
    workflowExecution.stepExecutions = workflowExecution.stepExecutions.map(stepExecution => {
      return stepExecution.id === currentStepExecution.id ? currentStepExecution : stepExecution;
    });

    logger.info('Workflow execution updated: ', workflowExecution);
    await this.workFlowExecutionAdapter.save(workflowExecution);

    const combinedParams = {
      ...previousStepWorkflow.params,
      ...executeWorkflowRequestDto.params,
    };

    logger.info('Executing back step task with params: ', combinedParams);
    const taskResult = await this.TaskAdapter.executeTask(
      new ExecuteTask(
        previousStepWorkflow.taskId,
        previousStepExecution.id,
        executeWorkflowRequestDto.messageType,
        combinedParams,
        executeWorkflowRequestDto.fileUrl,
        executeWorkflowRequestDto.lang,
      ),
    );

    logger.info('Task result: ', taskResult);
    return new Promise(resolve => {
      resolve(taskResult);
    });
  }

  async restartCurrentStep(workflowExecutionId: string): Promise<StartWorkflowResponseDto> {
    logger.info('Restarting current step: ', workflowExecutionId);
    const workflowExecution = await this.workFlowExecutionAdapter.getById(workflowExecutionId);
    logger.info('Workflow execution: ', workflowExecution);

    const currentStepExecution = workflowExecution.stepExecutions.filter(
      stepExecution => stepExecution.id === workflowExecution.currentStepExecutionId,
    )[0];

    currentStepExecution.status = StepExecutionStatus.CANCELLED;
    workflowExecution.stepExecutions = workflowExecution.stepExecutions.map(stepExecution => {
      return stepExecution.id === currentStepExecution.id ? currentStepExecution : stepExecution;
    });

    const stepExecutionRenewed = new StepExecution(
      uuidv4(),
      currentStepExecution.stepId,
      currentStepExecution.order,
      StepExecutionStatus.RUNNING,
    );

    workflowExecution.currentStepExecutionId = stepExecutionRenewed.id;

    workflowExecution.stepExecutions.push(stepExecutionRenewed);
    logger.info('Workflow execution updated: ', workflowExecution);
    await this.workFlowExecutionAdapter.save(workflowExecution);

    return new StartWorkflowResponseDto(
      workflowExecution.id,
      stepExecutionRenewed.id,
      stepExecutionRenewed.order,
    );
  }

  private async startStepByOrder(
    executeWorkflowRequestDto: ExecuteWorkflowRequestDto,
    stepOrder: number,
  ): Promise<any> {
    logger.info(
      `Workflow Use Case - StartStepByOrder - workflowExecution: ${executeWorkflowRequestDto.workflowExecutionId} to step: ${stepOrder}`,
    );

    const workflowExecution = await this.workFlowExecutionAdapter.getById(
      executeWorkflowRequestDto.workflowExecutionId,
    );

    const currentStepExecution = workflowExecution.stepExecutions.filter(
      stepExecution => stepExecution.id === workflowExecution.currentStepExecutionId,
    )[0];

    const workflow = await this.workFlowAdapter.get(workflowExecution.workflowId);

    const goToStepWorkflow = workflow.steps.filter(step => step.order === stepOrder)?.[0];

    if (!goToStepWorkflow) {
      throw new BusinessException(
        'Workflow',
        'Go to step not found',
        BusinessExceptionStatus.ITEM_NOT_FOUND,
        'GoToStepError',
      );
    }

    const goToStepExecution = new StepExecution(
      uuidv4(),
      goToStepWorkflow.id,
      goToStepWorkflow.order,
      StepExecutionStatus.STARTED,
    );

    workflowExecution.currentStepExecutionId = goToStepExecution.id;
    workflowExecution.stepExecutions.push(goToStepExecution);
    //TODO: remove last message from currentStepExecution
    currentStepExecution.status = StepExecutionStatus.FINISHED;

    workflowExecution.stepExecutions = workflowExecution.stepExecutions.map(stepExecution => {
      return stepExecution.id === currentStepExecution.id ? currentStepExecution : stepExecution;
    });

    await this.workFlowExecutionAdapter.save(workflowExecution);

    logger.info('Workflow execution updated: ' + JSON.stringify(workflowExecution));

    return this.execute(executeWorkflowRequestDto);
  }

  private async executePostMiddlewares(stepWorkflow: Step, currentStepExecution: StepExecution) {
    const postExecutionMiddlewares = stepWorkflow.middlewares.filter(
      m => m.type === MiddlewareType.POST_EXECUTION,
    );

    logger.info(
      `WorkflowUseCase - ExecutePostMiddlewares - executing post middlewares for step: ${
        stepWorkflow.id
      }, following the middlewares: ${JSON.stringify(postExecutionMiddlewares)}`,
    );

    const combinedMiddlewareResponse: Record<string, any> = {};

    for (const middleware of postExecutionMiddlewares) {
      try {
        const middlewareResponse = await this.TaskAdapter.executeSimplifiedTask(
          new ExecuteSimplifiedTask(
            middleware.taskId,
            currentStepExecution.id,
            middleware.params ?? {},
          ),
        );

        logger.info(
          `ExecutePostMiddlewares - mddleware response for middleware: ${
            middleware.name
          }: ${JSON.stringify(middlewareResponse)}`,
        );

        combinedMiddlewareResponse[middleware.name] =
          typeof middlewareResponse === 'object'
            ? { ...middlewareResponse, showOff: middleware.showOff }
            : { middlewareResponse, showOff: middleware.showOff };
      } catch (error) {
        logger.error(
          `ExecutePostMiddlewares -  error processing middleware ${
            middleware.name
          } Error:  ${JSON.stringify(error)}`,
        );
      }
    }

    logger.info(
      `WorkflowUseCase - ExecutePostMiddlewares: completed execution of middlewares for step: ${
        stepWorkflow.id
      }, with the following combined response: ${JSON.stringify(combinedMiddlewareResponse)}`,
    );

    return combinedMiddlewareResponse;
  }

  private async executePostStartMiddlewares(stepWorkflow: Step, parameters: any) {
    const postStartMiddlewares = stepWorkflow.middlewares.filter(
      m => m.type === MiddlewareType.POST_START,
    );

    logger.info(
      `WorkflowUseCase - ExecutePostStartMiddlewares - executing post start middlewares for step: ${
        stepWorkflow.id
      }, following the middlewares: ${JSON.stringify(postStartMiddlewares)}`,
    );

    let combinedMiddlewareResponse: any = {};

    for (const middleware of postStartMiddlewares) {
      try {
        let middlewareResponse: any;
        const combinedParams = {
          ...parameters,
          ...middleware.params,
        };

        if (middleware.category === MiddlewareToolCategory.INTEGRATION) {
          middlewareResponse = await this.integrationFactory
            .createIntegration(middleware.params['integration_name'])
            .handle(combinedParams);
        }

        logger.info(
          `WorkflowUseCase - executePostStartMiddlewares - mddleware response for middleware: ${
            middleware.name
          }: ${JSON.stringify(middlewareResponse)}`,
        );

        combinedMiddlewareResponse = {
          ...combinedMiddlewareResponse,
          ...middlewareResponse,
        };
      } catch (error) {
        logger.error(
          `WorkflowUseCase - executePostStartMiddlewares -  error processing middleware ${middleware.name} Error: ${error}`,
        );

        if (error instanceof IntegrationException) {
          throw new WorkflowExecutionException(
            {
              message: `${middleware.name} error: ${error.message}`,
              error: 'error',
            },
            error.status === IntegrationExceptionStatus.ITEM_NOT_FOUND
              ? HttpStatus.NOT_FOUND
              : HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }

        throw error;
      }
    }

    logger.info(
      `WorkflowUseCase - ExecutePostMiddlewares: completed execution of middlewares for step: ${
        stepWorkflow.id
      }, with the following combined response: ${JSON.stringify(combinedMiddlewareResponse)}`,
    );

    return combinedMiddlewareResponse;
  }
}
