import { logger } from '@edutalent/commons-sdk';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { HttpStatus } from '@nestjs/common';
import { CorrelationContextService } from '@common/services/correlation-context.service';

export const handleHttpError = (error: any, name: string): void => {
  const traceId = CorrelationContextService.getTraceId();

  // Enhanced error logging with trace ID and structured format
  logger.error('HTTP error occurred', {
    traceId,
    source: name,
    error: error.message,
    statusCode: error?.response?.status,
    statusText: error?.response?.statusText,
    url: error?.config?.url,
    method: error?.config?.method,
    responseData: error?.response?.data,
    requestData: error?.config?.data,
    timestamp: new Date().toISOString(),
    layer: 'HTTP_ERROR_HANDLER',
  });

  if (error?.response?.data) {
    let message = Array.isArray(error?.response?.data?.message)
      ? error?.response?.data?.message[0]
      : error?.response?.data?.message;

    // Ensure we always have a meaningful error message
    if (!message || message === null || message === undefined) {
      message =
        error?.response?.statusText ||
        error?.message ||
        `HTTP ${error?.response?.status} error` ||
        'Unknown HTTP error';
    }

    if (error?.response?.status === HttpStatus.NOT_FOUND) {
      throw new BusinessException(name, message, BusinessExceptionStatus.ITEM_NOT_FOUND);
    } else {
      throw new BusinessException(name, message, BusinessExceptionStatus.GENERAL_ERROR);
    }
  }

  // If no response data, create a meaningful error message from available information
  const fallbackMessage =
    error?.message || error?.code || error?.name || 'Network or connection error';

  throw new BusinessException(name, fallbackMessage, BusinessExceptionStatus.GENERAL_ERROR);
};
