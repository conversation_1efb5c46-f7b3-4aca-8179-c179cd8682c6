import { logger } from '@edutalent/commons-sdk';
import { Request } from 'express';

export function getOriginFromRequest(request: Request): string {
  const digaiClientDomain = Array.isArray(request.headers['x-digai-client-domain'])
    ? request.headers['x-digai-client-domain'][0]
    : request.headers['x-digai-client-domain'];

  const originHeader =
    digaiClientDomain || request.headers.host || request.headers.origin || request.headers.referer;

  if (!originHeader) {
    return 'default';
  }

  return originHeader;
}

export function getCookieDomainByOrigin(origin: string): string {
  switch (true) {
    case origin.includes('saleszap'):
      logger.info('Origin includes ciadetalentos');
      return '.saleszap.io';
    case origin.includes('fideleasy'):
      return '.fideleasy.io';
    case origin.includes('collectcash'):
      return '.collectcash.io';
    case origin.includes('digai'):
      return '.digai.ai';
    default:
      logger.info('Origin default');
      return '.infra-digai.io';
  }
}

export function getCookieDomainByRequest(request: Request): string {
  const origin = getOriginFromRequest(request);
  const cookieDomain = getCookieDomainByOrigin(origin);
  return cookieDomain;
}
