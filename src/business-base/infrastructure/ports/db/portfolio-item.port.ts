import { DbCommonPort } from '@common/db/ports/common.port';
import { PortfolioItemEntity } from '@business-base/domain/entities/portfolio-item.entity';
import { PaginationData } from '@common/pagination/types';
import { Paginated } from '@common/pagination/paginated';
import { PortfolioItemStatus } from '@common/enums';

export interface PortfolioItemPort extends DbC<PERSON>monPort<PortfolioItemEntity> {
  countByPortfolioId(portfolioId: string): Promise<number>;

  fetchAndQueuePendingItems(
    portfolioId: string,
    limit: number,
    queueUrl: string,
    createBatchAndQueueItems: (
      portfolioItemsIds: { id: string }[],
      portfolioId: string,
      queueUrl: string,
    ) => Promise<void>,
  ): Promise<void>;

  fetchAndQueueFollowUpItems(
    portfolioId: string,
    limit: number,
    queueUrl: string,
    afterMinutes: number,
    maxFollowUps: number,
    createBatchAndQueueItems: (
      portfolioItemsIds: { id: string }[],
      portfolioId: string,
      queueUrl: string,
      isFirstMessage: boolean,
      isFollowUp: boolean,
    ) => Promise<void>,
  ): Promise<void>;

  updateBatchToIdle(date: Date, batchSize: number): Promise<number>;

  findManyPaginated(data: any, pagination: PaginationData): Promise<Paginated<PortfolioItemEntity>>;

  findAllGroupedByStatus(portfolioId: string): Promise<Record<PortfolioItemStatus, number>>;

  findActiveByPortfolioIdsAndPhoneNumber(
    portfolioIds: string[],
    phoneNumber: string,
  ): Promise<PortfolioItemEntity[]>;

  findActiveByPortfolioIdAndLine(portfolioId: string, line: number): Promise<PortfolioItemEntity>;

  findToAnswerByPortfolioIdAndPhoneNumber(
    portfolioIds: string[],
    phoneNumber: string,
  ): Promise<PortfolioItemEntity[]>;

  findPendingByPortfolioIdAndPhoneNumber(
    portfolioIds: string[],
    phoneNumber: string,
  ): Promise<PortfolioItemEntity[]>;

  findPortfolioItemsWithAiOnlyInteractionCountByDate(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<number>;
}
