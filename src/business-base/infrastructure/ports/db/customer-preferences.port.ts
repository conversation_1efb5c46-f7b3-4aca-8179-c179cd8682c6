import { CustomerPreferencesEntity } from '@business-base/domain/entities/customer-preferences.entity';

export interface CustomerPreferencesPort {
  create(entity: CustomerPreferencesEntity): Promise<CustomerPreferencesEntity>;
  getById(customerId: string): Promise<CustomerPreferencesEntity>;
  update(customerId: string, entity: CustomerPreferencesEntity): Promise<CustomerPreferencesEntity>;
  delete(customerId: string): Promise<void>;
}
