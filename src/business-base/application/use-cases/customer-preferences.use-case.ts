import { Inject, Injectable } from '@nestjs/common';
import { CustomerPreferencesDto } from '@business-base/application/dto/customer-preferences.dto';
import { UpdateCustomerPreferencesDto } from '@business-base/application/dto/in/update-customer-preferences.dto';
import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
import { logger } from '@edutalent/commons-sdk';
import {
  CustomerPreferencesEntity,
  PortfolioPreferences,
} from '@business-base/domain/entities/customer-preferences.entity';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { CorrelationContextService } from '@common/services/correlation-context.service';

/**
 * Customer Preferences Use Case - Semi-Dynamic Property Handling Architecture
 *
 * This use case implements a semi-dynamic architecture for handling customer preference properties:
 *
 * 1. **DTO Layer (Manual Addition Required)**:
 *    - Developers must explicitly define new properties in DTOs and entities with proper validation
 *    - This ensures type safety and proper validation of new properties
 *
 * 2. **Service Layer (Automatic Handling)**:
 *    - Uses dynamic object spreading (...dynamicProperties) to automatically handle any properties defined in DTOs
 *    - No code changes needed in this layer when new properties are added to DTOs
 *
 * 3. **Repository/Adapter Layer (Automatic Handling)**:
 *    - Automatically persists any properties from entities using dynamic object assignment
 *    - No code changes needed when new properties are added
 *
 * **Goal**: Developers only need to define new properties in the type system (DTOs and entities)
 * with proper validation, and the rest of the system automatically handles these properties
 * through dynamic object operations.
 */
@Injectable()
export class CustomerPreferencesUseCase {
  constructor(
    @Inject('CustomerPreferencesPort')
    private readonly customerPreferencesAdapter: CustomerPreferencesPort,
  ) { }

  async create(
    customerId: string,
    createCustomerPreferencesDto: CustomerPreferencesDto,
  ): Promise<CustomerPreferencesDto> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Creating customer preferences', {
      traceId,
      customerId,
      operation: 'createCustomerPreferences',
      layer: 'USE_CASE',
    });

    // Check if preferences already exist
    const existingPreferences = await this.customerPreferencesAdapter.getById(customerId);
    if (existingPreferences) {
      throw new BusinessException(
        'CustomerPreferencesUseCase',
        `Customer preferences already exist for customerId: ${customerId}`,
        BusinessExceptionStatus.GENERAL_ERROR,
      );
    }

    // Create portfolio preferences dynamically if provided
    const portfolioPreferences = createCustomerPreferencesDto.portfolio
      ? new PortfolioPreferences(createCustomerPreferencesDto.portfolio)
      : undefined;

    // Extract dynamic properties (excluding portfolio to avoid conflicts)
    const { portfolio: _portfolio, ...dynamicProperties } = createCustomerPreferencesDto;

    const entity = new CustomerPreferencesEntity({
      customerId,
      portfolio: portfolioPreferences,
      ...dynamicProperties,
    });

    const createdEntity = await this.customerPreferencesAdapter.create(entity);

    logger.info('Customer preferences created successfully', {
      traceId,
      customerId,
      operation: 'createCustomerPreferences',
      layer: 'USE_CASE',
    });

    return new CustomerPreferencesDto({
      ...createdEntity, // Include all dynamic properties
      customerId,
      portfolio: createdEntity.portfolio ? { ...createdEntity.portfolio } : undefined,
    });
  }

  async findById(customerId: string): Promise<CustomerPreferencesDto | null> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Finding customer preferences by ID', {
      traceId,
      customerId,
      operation: 'findCustomerPreferencesById',
      layer: 'USE_CASE',
    });

    const entity = await this.customerPreferencesAdapter.getById(customerId);

    if (!entity) {
      logger.warn('Customer preferences not found', {
        traceId,
        customerId,
        operation: 'findCustomerPreferencesById',
        layer: 'USE_CASE',
      });

      return null;
    }

    logger.info('Customer preferences found successfully', {
      traceId,
      customerId,
      operation: 'findCustomerPreferencesById',
      layer: 'USE_CASE',
    });

    return new CustomerPreferencesDto({
      ...entity, // Include all dynamic properties
      customerId,
      portfolio: entity.portfolio ? { ...entity.portfolio } : undefined,
    });
  }

  async update(
    customerId: string,
    updateCustomerPreferencesDto: UpdateCustomerPreferencesDto,
  ): Promise<CustomerPreferencesDto> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Updating customer preferences', {
      traceId,
      customerId,
      operation: 'updateCustomerPreferences',
      layer: 'USE_CASE',
    });

    // Get existing preferences
    const existingEntity = await this.customerPreferencesAdapter.getById(customerId);
    if (!existingEntity) {
      throw new BusinessException(
        'CustomerPreferencesUseCase',
        `Customer preferences not found for customerId: ${customerId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    // Apply PUT semantics - replace with provided data only
    const updatedPortfolio = updateCustomerPreferencesDto.portfolio
      ? new PortfolioPreferences(updateCustomerPreferencesDto.portfolio)
      : undefined;

    // Create updated entity with PUT semantics (replace entire resource)
    const { portfolio: _portfolio, ...dynamicProperties } = updateCustomerPreferencesDto;

    const updatedEntity = new CustomerPreferencesEntity({
      // Only include properties from the payload (PUT semantics)
      ...dynamicProperties,
      customerId,
      portfolio: updatedPortfolio,
      // Keep only essential system properties from existing entity
      status: existingEntity.status,
      createdAt: existingEntity.createdAt,
      updatedAt: new Date(),
    });

    const result = await this.customerPreferencesAdapter.update(customerId, updatedEntity);

    logger.info('Customer preferences updated successfully', {
      traceId,
      customerId,
      operation: 'updateCustomerPreferences',
      layer: 'USE_CASE',
    });

    return new CustomerPreferencesDto({
      ...result, // Include all dynamic properties
      customerId,
      portfolio: result.portfolio ? { ...result.portfolio } : undefined,
    });
  }

  async delete(customerId: string): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Deleting customer preferences', {
      traceId,
      customerId,
      operation: 'deleteCustomerPreferences',
      layer: 'USE_CASE',
    });

    // Check if preferences exist
    const existingEntity = await this.customerPreferencesAdapter.getById(customerId);
    if (!existingEntity) {
      throw new BusinessException(
        'CustomerPreferencesUseCase',
        `Customer preferences not found for customerId: ${customerId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.customerPreferencesAdapter.delete(customerId);

    logger.info('Customer preferences deleted successfully', {
      traceId,
      customerId,
      operation: 'deleteCustomerPreferences',
      layer: 'USE_CASE',
    });
  }
}
