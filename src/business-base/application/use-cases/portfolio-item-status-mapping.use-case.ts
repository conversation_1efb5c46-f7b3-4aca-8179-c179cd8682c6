import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { MessageType, PortfolioItemStatus, RoleType } from '@common/enums';
import { PortfolioItemStatusMappingPort } from '@business-base/infrastructure/ports/db/portfolio-item-status-mapping.port';
import { PortfolioItemStatusMappingDto } from '@business-base/application/dto/in/portfolio-item-status-mapping.dto';
import { PortfolioItemStatusMappingEntity } from '@business-base/domain/entities/portfolio-item-status-mapping.entity';
import { plainToClass } from 'class-transformer';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { NectarGravarNegociacaoRequest } from '@business-base/infrastructure/dto/in/nectar-gravaracordo-resquest.dto';
import { NectarPort } from '@business-base/infrastructure/ports/soap/nectar.port';
import { PortfolioItemEntity } from '@business-base/domain/entities/portfolio-item.entity';
import { NectarGetBoletoAcordoRequest } from '@business-base/infrastructure/dto/in/nectar-getboletoacordo-resquest.dto';
import { PortfolioItemCustomDataEntity } from '@business-base/domain/entities/portfolio-item-custom-data.entity';

@Injectable()
export class PortfolioItemStatusMappingUseCase {
  constructor(
    @Inject('PortfolioItemStatusMappingPort')
    private readonly portfolioItemStatusMappingAdapter: PortfolioItemStatusMappingPort,
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioItemCustomDataPort')
    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
    @Inject(forwardRef(() => PortfolioItemUseCase))
    private readonly portfolioItemUseCase: PortfolioItemUseCase,
    @Inject('NectarPort')
    private readonly nectarAdapter: NectarPort,
  ) {}

  async create(
    createPortfolioItemStatusMappingDto: PortfolioItemStatusMappingDto,
  ): Promise<PortfolioItemStatusMappingDto> {
    logger.info(
      `Creating portfolio item status mapping for workflow: ${createPortfolioItemStatusMappingDto.workflowId}`,
    );

    const portfolioItemStatusMappingEntity = await this.portfolioItemStatusMappingAdapter.create(
      new PortfolioItemStatusMappingEntity(
        createPortfolioItemStatusMappingDto.workflowId,
        createPortfolioItemStatusMappingDto.postExecutionResponse,
        createPortfolioItemStatusMappingDto.responseKey,
        createPortfolioItemStatusMappingDto.portfolioItemStatus as PortfolioItemStatus,
      ),
    );

    return {
      ...createPortfolioItemStatusMappingDto,
      createdAt: portfolioItemStatusMappingEntity.createdAt,
      updatedAt: portfolioItemStatusMappingEntity.updatedAt,
    };
  }

  async listAllByWorkflowId(workflowId: string): Promise<PortfolioItemStatusMappingDto[]> {
    logger.info(`Finding all status mapping for workflowId: ${workflowId}`);
    const portfolioItemStatusMappings = await this.portfolioItemStatusMappingAdapter.getAll({
      workflowId: workflowId,
    });

    return portfolioItemStatusMappings.map(portfolioItemStatusMapping => {
      return this.createResponsePortfolioItemStatusMappingDto(portfolioItemStatusMapping);
    });
  }

  async deleteAllByWorkflowId(workflowId: string): Promise<PortfolioItemStatusMappingDto[]> {
    logger.info(`Deleting portfolioItemsStatusMappings for workflow id: ${workflowId}`);
    const portfolioItemStatusMappings = await this.portfolioItemStatusMappingAdapter.getAll({
      workflowId: workflowId,
    });

    await this.portfolioItemStatusMappingAdapter.deleteAllByWorkflowId(workflowId);

    return portfolioItemStatusMappings.map(portfolioItemStatusMapping => {
      return this.createResponsePortfolioItemStatusMappingDto(portfolioItemStatusMapping);
    });
  }

  async updatePortfolioItemStatus(
    workflowId: string,
    portfolioItemId: string,
    customerId: string,
    response: any,
  ): Promise<void> {
    logger.info(`Running status mapping for portfolio item: ${portfolioItemId}`);

    const portfolioItemStatusMappings = await this.portfolioItemStatusMappingAdapter.getAll({
      workflowId: workflowId,
    });

    logger.info(
      `Portfolio item: ${portfolioItemId} status mappings: ${JSON.stringify(
        portfolioItemStatusMappings,
      )}. Response: ${JSON.stringify(response)}`,
    );

    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      logger.error(`Portfolio item not found for id: ${portfolioItemId}`);
      return;
    }

    if (!portfolioItemStatusMappings) {
      logger.error(
        `Portfolio item status mapping not found for portfolio item: ${portfolioItemId}`,
      );
      return;
    }

    const responseKeys = new Set(portfolioItemStatusMappings.map(mapping => mapping.responseKey));
    logger.info(
      `Mapped response Keys for portfolio item: ${portfolioItemId}: ${JSON.stringify(
        responseKeys,
      )}`,
    );
    let countResponseKeys = 0;
    let status = null;
    for (const responseKey of responseKeys) {
      const responseString = this.getNestedValue(response, responseKey);
      if (responseString) {
        countResponseKeys++;
        status = portfolioItemStatusMappings.find(
          mapping => mapping.postExecutionResponse === responseString,
        );
      }
    }

    if (countResponseKeys === 0) {
      logger.info(
        `No response keys for status mapping found for portfolio item: ${portfolioItemId}`,
      );
      return;
    }

    if (countResponseKeys > 1) {
      logger.error(
        `Multiple response keys for status mapping found for portfolio item: ${portfolioItemId}`,
      );
      return;
    }

    if (countResponseKeys === 1) {
      logger.info(
        `Status mapping found for portfolio item: ${portfolioItemId} with status: ${status.portfolioItemStatus}`,
      );

      await this.portfolioItemUseCase.updateItemCurrentStatus(
        portfolioItemId,
        status.portfolioItemStatus,
      );

      if (status.portfolioItemStatus === PortfolioItemStatus.UNLINKED) {
        await this.portfolioItemUseCase.updateWaitingBusinessUserResponse(portfolioItemId, true);
      }

      if (
        this.checkIsPherfil(customerId) &&
        status.portfolioItemStatus === PortfolioItemStatus.SUCCEED
      ) {
        try {
          const portfolioItemCustomData =
            await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
              portfolioItem.customDataId,
              portfolioItem.id,
            );

          const selectedPaymentOption = response.middlewaresResponse['acordo-info'];

          if (selectedPaymentOption.acordo && selectedPaymentOption?.numeroParcelas) {
            await this.sendPaymentCode(
              portfolioItem,
              portfolioItemCustomData,
              selectedPaymentOption,
            );
          }
        } catch (error) {
          logger.error(
            `Error sending payment code for portfolio item: ${portfolioItemId}: ${JSON.stringify(
              error,
            )}`,
          );
        }
      }
    }
  }

  private async sendPaymentCode(
    portfolioItem: PortfolioItemEntity,
    portfolioItemCustomData: PortfolioItemCustomDataEntity,
    selectedPaymentOption: any,
  ) {
    try {
      const titulosList = selectedPaymentOption?.titulos;
      const idContrato = selectedPaymentOption?.idDoContrato;

      let numeroParcelas: string = selectedPaymentOption?.numeroParcelas;

      if (numeroParcelas && !isNaN(Number(numeroParcelas))) {
        numeroParcelas = numeroParcelas;
      } else {
        //se for a vista, o numero de parcelas deve ser 1
        numeroParcelas = '1';
      }

      const vencPrimParcela = portfolioItemCustomData.customData['VENC_PRIM_PARCELA'];

      const nectarGravarNegociacaoRequest = await this.getNegotiationOptionUpdated(
        idContrato,
        titulosList,
        numeroParcelas,
        vencPrimParcela,
      );

      await this.nectarAdapter.gravarNegociacao(
        new NectarGravarNegociacaoRequest({
          idCon: nectarGravarNegociacaoRequest.idCon,
          idServ: nectarGravarNegociacaoRequest.idServ,
          titulos: titulosList,
          plano: nectarGravarNegociacaoRequest.plano,
          codigoFaixa: nectarGravarNegociacaoRequest.codigoFaixa,
          descricaoFaixa: nectarGravarNegociacaoRequest.descricaoFaixa,
          parcelasNum: nectarGravarNegociacaoRequest.parcelasNum,
          valordesconto: nectarGravarNegociacaoRequest.valordesconto,
          vencimentoprimeira: nectarGravarNegociacaoRequest.vencimentoprimeira,
          valorprimeira: nectarGravarNegociacaoRequest.valorprimeira,
          valororiginal: nectarGravarNegociacaoRequest.valororiginal,
          valorcorrigido: nectarGravarNegociacaoRequest.valorcorrigido,
          valornegociar: nectarGravarNegociacaoRequest.valornegociar,
          valordemais: nectarGravarNegociacaoRequest.valordemais,
          tiponegociacao: '3',
          boletodisponivel: '1',
          tpDesconto: '2',
          percDescAplicNoPrincipal: nectarGravarNegociacaoRequest.percDescAplicNoPrincipal,
          percDescAplicNaCorrecao: nectarGravarNegociacaoRequest.percDescAplicNaCorrecao,
        }),
      );

      const document = portfolioItemCustomData.customData['CPF_DO_CLIENTE'];

      const getDadosDividaResponse = await this.nectarAdapter.getDadosDivida(document);

      const contractInfo = getDadosDividaResponse.GetDadosDividaResult.Contrato.Contrato.filter(
        c => c.IDCON == idContrato,
      )[0];

      if (contractInfo.Acordo?.Acordo) {
        const latestAcordo = contractInfo.Acordo.Acordo.sort(
          (a, b) => new Date(b.DataAceite).getTime() - new Date(a.DataAceite).getTime(),
        )[0];

        const boletoId = latestAcordo.AcordoParcela.AcordoParcela[0].IDParcela;

        const request = new NectarGetBoletoAcordoRequest({
          tipo: 'ACORDO',
          idboleto: boletoId,
          idcon: contractInfo.IDCON,
          plano: nectarGravarNegociacaoRequest.plano,
          idserv: '0',
          tipoenvio: '1',
          gerarPdf: '1',
          especiePagamento: 'boleto',
        });

        const getBoletoAcordoResponse = await this.nectarAdapter.getBoletoAcordo(request);
        const boletoAcordo = getBoletoAcordoResponse.GetBoletoAcordoResult.BoletoAcordo;

        await this.portfolioItemUseCase.sendDirectMessage(portfolioItem.id, {
          message: `Segue abaixo linha digitável e o PDF do boleto para pagamento com o valor de R$ ${nectarGravarNegociacaoRequest.valorprimeira} com o vencimento em ${nectarGravarNegociacaoRequest.vencimentoprimeira}.`,
          messageType: MessageType.TEXT,
          roleType: RoleType.ASSISTANT,
        });

        await this.portfolioItemUseCase.sendDirectMessage(portfolioItem.id, {
          message: `${boletoAcordo.BoletoAcordo.LinhaDigitavel}`,
          messageType: MessageType.TEXT,
          roleType: RoleType.ASSISTANT,
        });

        await this.portfolioItemUseCase.sendDirectMessageBase64(
          portfolioItem.id,
          {
            message: 'Boleto para pagamento.',
            messageType: MessageType.PDF,
            roleType: RoleType.ASSISTANT,
          },
          boletoAcordo.BoletoAcordo.RetornoPDF,
          'boleto_acordo.pdf',
        );
      }
    } catch (error) {
      await this.portfolioItemUseCase.updateWaitingBusinessUserResponse(portfolioItem.id, true);
      logger.error(
        `Error sending payment code for portfolio item: ${portfolioItem.id}: ${JSON.stringify(
          error,
        )}`,
      );
    }
  }

  private checkIsPherfil(customerId: string): boolean {
    return process.env.PHERFIL_CUSTOMER_ID && customerId == process.env.PHERFIL_CUSTOMER_ID;
  }

  private createResponsePortfolioItemStatusMappingDto(
    portfolioItemStatusMappingEntity: PortfolioItemStatusMappingEntity,
  ): PortfolioItemStatusMappingDto {
    return plainToClass(PortfolioItemStatusMappingDto, portfolioItemStatusMappingEntity);
  }

  private getNestedValue(obj: any, keyString: string): any {
    return keyString.split('.').reduce((acc, key) => acc && acc[key], obj);
  }

  private async getNegotiationOptionUpdated(
    idContrato: any,
    titulosList: any,
    numeroParcelas: string,
    vencPrimParcela: string,
  ) {
    const opcoesNegociacaoList = await this.nectarAdapter.getOpcoesNegociacaoWithMaxDiscountLessOne(
      idContrato,
      titulosList,
      numeroParcelas,
      vencPrimParcela,
    );

    const nectarGravarNegociacaoRequestList: NectarGravarNegociacaoRequest[] = [];

    opcoesNegociacaoList.flatMap(
      ({
        Plano,
        CodigoFaixa,
        DescricaoFaixa,
        Parcelas,
        ValorCorrigido,
        ValorOriginal,
        ValorDesconto,
        ValorNegociar,
        PercentualDescontoAplicadoNoPrincipal,
        PercentualDescontoAplicadoNaCorrecao,
      }) =>
        Parcelas.Parcelas.every(parcela => {
          const nectarGravarNegociacaoRequest = new NectarGravarNegociacaoRequest({
            idCon: idContrato,
            idServ: '1',
            plano: Plano,
            codigoFaixa: CodigoFaixa,
            descricaoFaixa: DescricaoFaixa,
            parcelasNum: !isNaN(Number(parcela.Numero)) ? parcela.Numero : '1',
            valordesconto: ValorDesconto,
            vencimentoprimeira: new Date(parcela.DataVencimento).toLocaleDateString('pt-BR'),
            valorprimeira: parcela.ValorEntrada,
            valororiginal: ValorOriginal,
            valorcorrigido: ValorCorrigido,
            valornegociar: ValorNegociar,
            valordemais: parcela.ValorDemaisParcelas ?? '0',
            tiponegociacao: '3',
            boletodisponivel: '1',
            tpDesconto: '1',
            percDescAplicNoPrincipal: PercentualDescontoAplicadoNoPrincipal,
            percDescAplicNaCorrecao: PercentualDescontoAplicadoNaCorrecao,
          });

          nectarGravarNegociacaoRequestList.push(nectarGravarNegociacaoRequest);
        }),
    );

    const nectarGravarNegociacaoRequest = nectarGravarNegociacaoRequestList.filter(
      p => p.parcelasNum == numeroParcelas,
    )[0];

    return nectarGravarNegociacaoRequest;
  }

  private addBusinessDays(date: Date, days: number): Date {
    let count = 0;
    while (count < days) {
      date.setDate(date.getDate() + 1);
      if (date.getDay() !== 0 && date.getDay() !== 6) {
        count++;
      }
    }
    return date;
  }
}
