import { forwardRef, Inject, Injectable } from '@nestjs/common';
import * as readline from 'node:readline';
import { randomUUID as uuidv4 } from 'crypto';
import { plainToClass } from 'class-transformer';
import { parse } from 'fast-csv';
import { logger } from '@edutalent/commons-sdk';
import { PortfolioEntity } from '@business-base/domain/entities/portfolio.entity';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { PortfolioDto } from '@business-base/application/dto/in/portfolio.dto';
import { UpdatePortfolioDto } from '@business-base/application/dto/in/update-portfolio.dto';
import { ResponsePortfolioDto } from '@business-base/application//dto/out/response-portfolio.dto';
import { PortfolioUpdateImportDataDto } from '@business-base/application/dto/in/portfolio-update-import-data.dto';
import { PortfolioItemDto } from '@business-base/application/dto/in/portfolio-item.dto';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import {
  PortfolioExecutionStatus,
  PortfolioImportStatus,
  PortfolioItemStatus,
  RecordStatus,
} from '@common/enums';
import { S3Service } from '@common/s3/s3.service';
import { SQSService } from '@common/sqs/sqs.service';
import { CustomerPort } from '@business-base/infrastructure/ports/db/customer.port';
import { PortfolioItemImportErrorPort } from '@business-base/infrastructure/ports/db/portfolio-item-import-error.port';
import { PortfolioItemImportErrorEntity } from '@business-base/domain/entities/portfolio-item-import-error.entity';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';
import { PortfolioPerformanceResponseDto } from '@business-base/infrastructure/dto/out/portfolio-performance-response.dto';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { Readable } from 'node:stream';
import { createObjectCsvStringifier } from 'csv-writer';
import { IntegrationException } from '@common/exception/types/IntegrationException';
import { extractNestedKeys } from '@common/utils/extract-nested-keys';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { AvailableColumn } from '@business-base/application/dto/available-export-column.dto';
import {
  portfolioColumns,
  portfolioItemColumns,
} from '@business-base/application/use-cases/portfolio-export-consts';
import { getNestedProperty, sanitizeCsvValue } from '@common/utils/csv-util';
import { PortfolioItemEntity } from '@business-base/domain/entities/portfolio-item.entity';
import { InfraWorkflowPort } from '@business-base/infrastructure/ports/http/workflow.port';
import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
import {
  CustomerPreferencesDto,
  CustomImportConfigDto,
} from '@business-base/application/dto/customer-preferences.dto';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class PortfolioUseCase {
  private DEFAULT_DELIMITER = ',';
  private readonly portfolioImportFilesBucketName: string;

  constructor(
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,
    @Inject('CustomerPort')
    private readonly customerAdapter: CustomerPort,
    @Inject('PortfolioItemImportErrorPort')
    private readonly portfolioItemImportErrorAdapter: PortfolioItemImportErrorPort,
    private readonly s3Service: S3Service,
    private readonly sqsService: SQSService,
    @Inject(forwardRef(() => PortfolioItemUseCase))
    private readonly portfolioItemUseCase: PortfolioItemUseCase,
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioItemCustomDataPort')
    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
    @Inject('MiddlewareResponseOutputPort')
    private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort,
    @Inject('InfraWorkflowPort')
    private readonly workflowAdapter: InfraWorkflowPort,
    private readonly customerPreferencesUseCase: CustomerPreferencesUseCase,
  ) {
    this.portfolioImportFilesBucketName = process.env.PORTFOLIO_IMPORT_FILES_BUCKET;
  }

  async create(
    createPortfolioDto: PortfolioDto,
    file: Express.Multer.File,
    customerId: string,
  ): Promise<ResponsePortfolioDto> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Starting portfolio creation with file import', {
      traceId,
      customerId,
      portfolioName: createPortfolioDto.name,
      operation: 'createPortfolioWithFile',
      layer: 'USE_CASE',
    });

    // Generate portfolio ID early for tracing
    createPortfolioDto.id = uuidv4();

    // Retrieve customer preferences
    const customerPreferences = await this.getCustomerPreferences(customerId);

    // parse the csv headers from the uploaded file using customer preference delimiter
    const delimiter =
      customerPreferences?.portfolio?.customImportConfig?.delimiter || this.DEFAULT_DELIMITER;

    const csvHeaders = await this.parseCsvHeaders(file, delimiter);

    // Apply header mapping from customImportConfig if available. Otherwise, use original headers
    const mappedHeaders = this.applyHeaderMapping(
      csvHeaders,
      customerPreferences?.portfolio?.customImportConfig?.headerMapping,
      customerPreferences?.portfolio?.customImportConfig?.additionalHeaders,
    );

    // check if all workflow variables are present in the csv headers
    const variables = await this.workflowAdapter.getWorkflowVariables(
      createPortfolioDto.workflowId,
    );

    await this.checkIfPortfolioCsvHasAllWorkflowVariables(mappedHeaders, variables);

    const customer = await this.customerAdapter.get(customerId);

    const processingRateLimit = Number(process.env.DEFAULT_PORTFOLIO_PROCESSING_RATE_LIMIT) || 0;

    const fileUploadResult = await this.s3Service.uploadFileMultPart(
      file,
      this.portfolioImportFilesBucketName,
      decodeURIComponent(`${customer.id}/${createPortfolioDto.id}.csv`),
    );

    const portfolio = await this.portfolioAdapter.create(
      this.createPortfolioEntityWithPreferences({
        portfolioDto: createPortfolioDto,
        customerId: customer.id,
        originalFileName: file.originalname,
        fileUrl: fileUploadResult.location,
        processingRateLimit,
        preferences: customerPreferences,
      }),
    );

    this.countPortfolioImportedLines(customer.id, portfolio.id, customer.segment);

    return this.createResponsePortfolioDto(portfolio);
  }

  private async parseCsvHeaders(
    file: Express.Multer.File,
    delimiter: string = this.DEFAULT_DELIMITER,
  ): Promise<string[]> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      // Use split('\n') to efficiently get the first line without reading the entire file
      const firstLine = file.buffer.toString().split('\n')[0].trim();

      if (!firstLine) {
        throw new Error('CSV file is empty or contains no headers');
      }

      // Split by delimiter and convert to uppercase
      const headers = firstLine
        .toUpperCase()
        .split(delimiter)
        .map(header => header.trim());

      if (headers.length === 0) {
        throw new Error('No valid headers found in CSV file');
      }

      logger.info('CSV headers parsed successfully', {
        traceId,
        operation: 'parseCsvHeaders',
        layer: 'USE_CASE',
        delimiter,
        headerCount: headers.length,
        headers: headers,
      });

      return headers;
    } catch (error) {
      logger.error('Failed to parse CSV headers', {
        traceId,
        operation: 'parseCsvHeaders',
        layer: 'USE_CASE',
        delimiter,
        error: error.message,
        errorType: error.constructor?.name,
      });
      throw new BusinessException(
        'PortfolioUseCase',
        `Failed to parse CSV headers with delimiter '${delimiter}': ${error.message}`,
        BusinessExceptionStatus.INVALID_INPUT,
      );
    }
  }

  private async checkIfPortfolioCsvHasAllWorkflowVariables(
    csvHeaders: string[],
    workflowVariables: string[],
  ): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Checking if portfolio CSV has all workflow variables', {
      traceId,
      operation: 'checkIfPortfolioCsvHasAllWorkflowVariables',
      layer: 'USE_CASE',
      csvHeaderCount: csvHeaders.length,
      workflowVariableCount: workflowVariables.length,
    });

    const csvHeadersSet = new Set(csvHeaders.map(header => header.toUpperCase()));
    const workflowVariablesSet = new Set(workflowVariables.map(variable => variable.toUpperCase()));
    const missingVariables = Array.from(workflowVariablesSet).filter(
      variable => !csvHeadersSet.has(variable),
    );
    if (missingVariables.length > 0) {
      logger.error('Missing required workflow variables in the csv headers', {
        traceId,
        operation: 'checkIfPortfolioCsvHasAllWorkflowVariables',
        layer: 'USE_CASE',
        missingVariables,
      });

      throw new BusinessException(
        'Portfolio-use-case',
        `Missing required workflow variables in the csv headers: ${missingVariables
          .join(', ')
          .toUpperCase()}`,
        BusinessExceptionStatus.INVALID_INPUT,
      );
    }
  }

  async findAll(customerId: string): Promise<ResponsePortfolioDto[]> {
    logger.debug('Finding all portfolios');
    let portfolios = await this.portfolioAdapter.getAll({ customerId });
    //order by createdAt desc
    portfolios = portfolios.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    return portfolios.map(portfolio => {
      return this.createResponsePortfolioDto(portfolio);
    });
  }

  async findAllImporting(): Promise<ResponsePortfolioDto[]> {
    logger.debug('Finding all portfolios importing');
    const portfolios = await this.portfolioAdapter.getAll(
      {
        importStatus: PortfolioImportStatus.PROCESSING,
        executionStatus: PortfolioExecutionStatus.QUEUED,
        importFinishedAt: null,
      },
      {
        id: true,
        processedQuantity: true,
        totalQuantity: true,
        workExpression: true,
        executeImmediately: true,
      },
    );

    return portfolios.map(portfolio => {
      return this.createResponsePortfolioDto(portfolio);
    });
  }

  async findAllImportingByCustomerId(customerId: string): Promise<ResponsePortfolioDto[]> {
    logger.debug('Finding all portfolios importing');
    const portfolios = await this.portfolioAdapter.getAll(
      {
        importStatus: PortfolioImportStatus.PROCESSING,
        executionStatus: PortfolioExecutionStatus.QUEUED,
        importFinishedAt: null,
        customerId,
      },
      {
        id: true,
        processedQuantity: true,
        totalQuantity: true,
        workExpression: true,
        executeImmediately: true,
      },
    );

    return portfolios.map(portfolio => {
      return this.createResponsePortfolioDto(portfolio);
    });
  }

  async findAllQueuedToStart(): Promise<ResponsePortfolioDto[]> {
    logger.debug('Finding all portfolios to start');
    const portfolios = await this.portfolioAdapter.getAll(
      {
        importStatus: PortfolioImportStatus.SUCCESS,
        executionStatus: {
          in: [PortfolioExecutionStatus.QUEUED],
        },
        executeImmediately: true,
        importFinishedAt: { not: null },
        status: RecordStatus.ACTIVE,
      },
      {
        id: true,
        name: true,
        customerId: true,
        workExpression: true,
        followUpExpression: true,
        followUpWorkflowId: true,
        maxFollowUps: true,
        followUpAfter: true,
        executionStatus: true,
      },
    );

    return portfolios.map(portfolio => {
      return this.createResponsePortfolioDto(portfolio);
    });
  }

  async findAllExecutingToRestart(): Promise<ResponsePortfolioDto[]> {
    logger.debug('Finding all portfolios executing to restart...');
    const portfolios = await this.portfolioAdapter.getAll(
      {
        importStatus: PortfolioImportStatus.SUCCESS,
        executionStatus: {
          in: [PortfolioExecutionStatus.EXECUTING],
        },
        executeImmediately: true,
        importFinishedAt: { not: null },
        status: RecordStatus.ACTIVE,
      },
      {
        id: true,
        name: true,
        customerId: true,
        workExpression: true,
        followUpExpression: true,
        followUpWorkflowId: true,
        maxFollowUps: true,
        followUpAfter: true,
        executionStatus: true,
      },
    );

    return portfolios.map(portfolio => {
      return this.createResponsePortfolioDto(portfolio);
    });
  }

  async findAllPausedOrCancelledOrFinishedToStop(): Promise<ResponsePortfolioDto[]> {
    logger.debug('Finding all portfolios paused or cancelled to stop');
    const portfolios = await this.portfolioAdapter.getAll(
      {
        importStatus: PortfolioImportStatus.SUCCESS,
        executionStatus: {
          in: [
            PortfolioExecutionStatus.PAUSED,
            PortfolioExecutionStatus.CANCELLED,
            PortfolioExecutionStatus.FINISHED,
          ],
        },
        importFinishedAt: { not: null },
        status: RecordStatus.ACTIVE,
      },
      {
        id: true,
        name: true,
        customerId: true,
        workExpression: true,
        executionStatus: true,
      },
    );

    return portfolios.map(portfolio => {
      return this.createResponsePortfolioDto(portfolio);
    });
  }

  async findAllToFinish(): Promise<{ id: string }[]> {
    logger.debug(
      'Finding all portfolios executing with no PENDING, IN_PROGRESS or FOLLOWED_UP to stop...',
    );
    const portfoliosIds = await this.portfolioAdapter.findAllPortfoliosToFinish();

    return portfoliosIds;
  }

  async findById(portfolioId: string, customerId: string): Promise<ResponsePortfolioDto> {
    const portfolio = await this.getPortfolioEntity(portfolioId, customerId);

    return this.createResponsePortfolioDto(portfolio);
  }

  async findByIdInternal(portfolioId: string): Promise<ResponsePortfolioDto> {
    const portfolio = await this.getPortfolioEntityInternal(portfolioId);

    return this.createResponsePortfolioDto(portfolio);
  }

  async findAllPerformance(customerId: string): Promise<PortfolioPerformanceResponseDto[]> {
    const portfolios = await this.portfolioAdapter.getAll({ customerId });

    const response: PortfolioPerformanceResponseDto[] = [];

    for (const portfolio of portfolios) {
      const stats = await this.portfolioItemUseCase.findAllItemsGroupedByStatus(portfolio.id);
      response.push({
        portfolioId: portfolio.id,
        portfolioName: portfolio.name,
        executionStatus: portfolio.executionStatus,
        stats,
      });
    }

    return response;
  }

  async findPerformanceById(
    portfolioId: string,
    customerId: string,
  ): Promise<{
    portfolioId: string;
    portfolioName: string;
    stats: Record<PortfolioItemStatus, number>;
  }> {
    const portfolio = await this.getPortfolioEntity(portfolioId, customerId);

    const portfolioPerformance = await this.portfolioItemUseCase.findAllItemsGroupedByStatus(
      portfolioId,
    );

    return {
      portfolioId: portfolio.id,
      portfolioName: portfolio.name,
      stats: portfolioPerformance,
    };
  }

  async update(
    portfolioId: string,
    customerId: string,
    updatePortfolioDto: UpdatePortfolioDto,
  ): Promise<ResponsePortfolioDto> {
    logger.info(`Updating portfolio with id: ${portfolioId}`);
    const portfolioEntity = await this.getPortfolioEntity(portfolioId, customerId);

    const updatedPortfolioEntity = await this.portfolioAdapter.update({
      ...portfolioEntity,
      name: updatePortfolioDto.name,
      workExpression: updatePortfolioDto.workExpression,
      idleAfter: updatePortfolioDto.idleAfter,
    });

    return this.createResponsePortfolioDto(updatedPortfolioEntity);
  }

  async updateImportData(importData: PortfolioUpdateImportDataDto): Promise<ResponsePortfolioDto> {
    const {
      id,
      importStatus,
      importFinishedAt,
      totalQuantity,
      processedQuantity,
      totalFailedQuantity,
      totalSuccessQuantity,
    } = importData;
    logger.info(`Updating import status to: ${importStatus} of portfolio with id: ${id}`);
    const portfolioEntity = await this.portfolioAdapter.get(id);

    if (!portfolioEntity) {
      throw new BusinessException(
        'Portfolio-use-case',
        `Portfolio with id ${id} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const updatedPortfolioEntity = await this.portfolioAdapter.update({
      ...portfolioEntity,
      importStatus,
      importFinishedAt,
      processedQuantity,
      totalFailedQuantity,
      totalSuccessQuantity,
      totalQuantity,
    });

    return this.createResponsePortfolioDto(updatedPortfolioEntity);
  }

  async updateExecutionStatus(
    portfolioId: string,
    executionStatus: PortfolioExecutionStatus,
  ): Promise<void> {
    logger.info(
      `Updating execution status to: ${executionStatus} of portfolio with id: ${portfolioId}`,
    );
    const portfolioEntity = await this.portfolioAdapter.get(portfolioId);

    if (!portfolioEntity) {
      throw new BusinessException(
        'Portfolio-use-case',
        `Portfolio with id ${portfolioId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.portfolioAdapter.update({
      ...portfolioEntity,
      executionStatus,
    });
  }

  async updateExecutionStatusToQueued(
    portfolioId: string,
    customerId: string,
  ): Promise<ResponsePortfolioDto> {
    const portfolio = await this.getPortfolioEntity(portfolioId, customerId);

    if (
      portfolio.executionStatus === PortfolioExecutionStatus.EXECUTING ||
      portfolio.executionStatus === PortfolioExecutionStatus.INBOUND
    ) {
      logger.info(
        `Portfolio ${portfolioId} already running. Current status: ${portfolio.executionStatus}`,
      );
      return this.createResponsePortfolioDto(portfolio);
    }

    const updatedPortfolio = {
      ...portfolio,
      executionStatus: PortfolioExecutionStatus.QUEUED,
      executeImmediately: true,
    };

    await this.portfolioAdapter.update(updatedPortfolio);

    logger.info(`Update execution status to QUEUED of portfolio with id: ${portfolioId}`);

    return this.createResponsePortfolioDto(updatedPortfolio);
  }

  async updateExecutionStatusToPaused(
    portfolioId: string,
    customerId: string,
  ): Promise<ResponsePortfolioDto> {
    const portfolio = await this.getPortfolioEntity(portfolioId, customerId);

    if (portfolio.executionStatus === PortfolioExecutionStatus.PAUSED) {
      logger.info(`Portfolio ${portfolioId} already paused.`);
      return this.createResponsePortfolioDto(portfolio);
    }

    const updatedPortfolio = {
      ...portfolio,
      executionStatus: PortfolioExecutionStatus.PAUSED,
    };

    await this.portfolioAdapter.update(updatedPortfolio);

    logger.info(`Update execution status to PAUSED of portfolio with id: ${portfolioId}`);

    return this.createResponsePortfolioDto(updatedPortfolio);
  }

  async updateExecutionStatusToCancelled(
    portfolioId: string,
    customerId: string,
  ): Promise<ResponsePortfolioDto> {
    const portfolio = await this.getPortfolioEntity(portfolioId, customerId);
    const portfolioItems = await this.portfolioItemAdapter.getAll({ portfolioId });

    if (portfolio.executionStatus === PortfolioExecutionStatus.CANCELLED) {
      logger.info(`Portfolio ${portfolioId} already cancelled.`);
      return this.createResponsePortfolioDto(portfolio);
    }

    const updatedPortfolio = {
      ...portfolio,
      executionStatus: PortfolioExecutionStatus.CANCELLED,
    };

    await this.portfolioAdapter.update(updatedPortfolio);
    for (const portfolioItem of portfolioItems) {
      if (portfolioItem.currentStatus !== PortfolioItemStatus.SUCCEED) {
        await this.portfolioItemUseCase.updateItemCurrentStatus(
          portfolioItem.id,
          PortfolioItemStatus.CANCELLED,
          'Cancelled by user: portfolio cancelled.',
        );
      }
    }

    logger.info(`Update execution status to CANCELLED of portfolio with id: ${portfolioId}`);

    return this.createResponsePortfolioDto(updatedPortfolio);
  }

  async delete(portfolioId: string, customerId: string): Promise<ResponsePortfolioDto> {
    logger.info(`Deleting portfolio with id: ${portfolioId}`);
    const portfolio = await this.getPortfolioEntity(portfolioId, customerId);

    if (!portfolio) {
      throw new BusinessException(
        'Portfolio-use-case',
        `Portfolio with id ${portfolioId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const deletedPortfolioEntity = await this.portfolioAdapter.delete(portfolioId);
    return this.createResponsePortfolioDto(deletedPortfolioEntity);
  }

  async getAvailableColumns(portfolioId: string): Promise<AvailableColumn[]> {
    logger.info(`Getting available columns for portfolio with id: ${portfolioId}`);
    const portfolio = await this.portfolioAdapter.get(portfolioId);

    if (!portfolio) {
      throw new BusinessException(
        'Portfolio-use-case',
        `Portfolio with id ${portfolioId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const portfolioItems = (await this.getPortfolioItemSampling(portfolioId)).filter(Boolean);

    const customDataKeys = new Set<string>();
    const middlewareDataKeys = new Set<string>();

    for (const item of portfolioItems) {
      if (item.customDataId) {
        const customDataScanResult = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
          item.customDataId,
          item.id,
        );

        if (customDataScanResult) {
          extractNestedKeys(customDataScanResult.customData).forEach(key =>
            customDataKeys.add(key),
          );
        }
      }

      if (item.middlewareResponseOutputId) {
        const middlewareResult = await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
          item.middlewareResponseOutputId,
          item.id,
        );

        if (middlewareResult) {
          extractNestedKeys(middlewareResult.data).forEach(key => middlewareDataKeys.add(key));
        }
      }
    }

    const customDataColumns: AvailableColumn[] = Array.from(customDataKeys).map(key => ({
      entity: 'customData',
      field: key,
      displayName: `Custom Data: ${key.replace(/\./g, ' > ')}`,
    }));

    const middlewareDataColumns: AvailableColumn[] = Array.from(middlewareDataKeys).map(key => ({
      entity: 'middlewareData',
      field: key,
      displayName: `Middleware Data: ${key.replace(/\./g, ' > ')}`,
    }));

    const allColumns: AvailableColumn[] = [
      ...portfolioColumns,
      ...portfolioItemColumns,
      ...customDataColumns,
      ...middlewareDataColumns,
    ];

    return allColumns;
  }

  async exportPortfolioItemsToCsv(
    portfolioId: string,
    selectedColumns: AvailableColumn[],
  ): Promise<Readable> {
    const portfolio = await this.portfolioAdapter.get(portfolioId);

    if (!portfolio) {
      throw new BusinessException(
        'Portfolio-use-case',
        `Portfolio with id ${portfolioId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const portfolioItems = await this.portfolioItemAdapter.getAll({ portfolioId });

    if (!portfolioItems || portfolioItems.length === 0) {
      throw new BusinessException(
        'Portfolio-use-case',
        `Portfolio with id ${portfolioId} has no items to export`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    if (selectedColumns.length === 0) {
      selectedColumns = await this.getAvailableColumns(portfolioId);
    }

    const csvStringifier = createObjectCsvStringifier({
      header: selectedColumns.map(col => ({
        id: col.field,
        title: col.displayName,
      })),
    });

    const readableStream = new Readable({
      read() { },
    });

    try {
      readableStream.push(csvStringifier.getHeaderString());

      for (const item of portfolioItems) {
        const record: any = {};
        let customData: any = {};
        let middlewareData: any = {};

        if (item.customDataId) {
          customData = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
            item.customDataId,
            item.id,
          );
        }

        if (item.middlewareResponseOutputId) {
          middlewareData = await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
            item.middlewareResponseOutputId,
            item.id,
          );
        }

        for (const col of selectedColumns) {
          let value: any;
          if (col.entity === 'portfolioItem') {
            value = item[col.field] ?? '';
          } else if (col.entity === 'customData') {
            value = getNestedProperty(customData, `customData.${col.field}`);
          } else if (col.entity === 'middlewareData') {
            value = getNestedProperty(middlewareData, `data.${col.field}`);
          } else if (col.entity === 'portfolio') {
            value = portfolio[col.field] ?? '';
          }
          record[col.field] = sanitizeCsvValue(value);
        }

        readableStream.push(csvStringifier.stringifyRecords([record]));
      }

      readableStream.push(null);
    } catch (error) {
      logger.error(
        `Error generating CSV stream for portfolio id : ${portfolioId}. Error: ${JSON.stringify(
          error,
        )}`,
        error,
      );
      readableStream.emit('error', error);
      readableStream.push(null);
    }

    return readableStream;
  }

  async processPortfolioImported(fileKey: string, portfolioId: string): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();
    logger.info('Processing portfolio import', {
      traceId,
      portfolioId,
      operation: 'processPortfolioImported',
      layer: 'USE_CASE',
      fileKey,
    });

    const portfolio = await this.portfolioAdapter.get(portfolioId);
    const customer = await this.customerAdapter.get(portfolio.customerId);

    // Retrieve customer preferences for delimiter configuration
    const customerPreferences = await this.getCustomerPreferences(customer.id);

    // Priority: customImportConfig.delimiter > portfolio.delimiter > default ','
    const delimiter =
      customerPreferences?.portfolio?.customImportConfig?.delimiter || this.DEFAULT_DELIMITER;

    logger.info('Processing portfolio import with custom delimiter', {
      portfolioId,
      customerId: customer.id,
      operation: 'processPortfolioImported',
      layer: 'USE_CASE',
      delimiter,
      isCustomDelimiter: !!customerPreferences?.portfolio?.customImportConfig?.delimiter,
    });

    const s3Stream = await this.getOriginalFileStream(customer.id, portfolioId);
    if (!s3Stream) {
      return;
    }

    const queueUrl = this.sqsService.getImportItemQueueByCustomer(customer.id);
    const sqsBatchSize = 10; // SQS permite o maximo de 10 mensagens por batch
    const batch: any[] = [];

    const parser = parse({ headers: true, delimiter });
    let line = 1;
    let processedCount = 0;

    logger.info('Starting portfolio import processing', {
      traceId,
      portfolioId,
      customerId: customer.id,
      operation: 'processPortfolioImported',
      layer: 'USE_CASE',
      fileKey,
      delimiter,
      isCustomDelimiter: !!customerPreferences?.portfolio?.customImportConfig?.delimiter,
    });

    s3Stream
      .pipe(parser)
      .on('data', async data => {
        logger.debug(`Processing line ${line} of portfolio import`, {
          traceId,
          portfolioId,
          customerId: customer.id,
          operation: 'processPortfolioImported',
          layer: 'USE_CASE',
          data,
        });

        line++;
        processedCount++;

        // Apply header mapping with validation
        const mappedData = this.applyHeaderMapping(
          data,
          customerPreferences?.portfolio?.customImportConfig?.headerMapping,
        );

        batch.push({
          portfolioItem: mappedData,
          line,
        });

        while (batch.length >= sqsBatchSize) {
          await this.createProcessBatch(portfolioId, batch.splice(0, sqsBatchSize), queueUrl);
        }
      })
      .on('end', async () => {
        if (batch.length > 0) {
          await this.createProcessBatch(portfolioId, batch, queueUrl);
        }

        await this.updateImportData({
          id: portfolioId,
          importStatus: PortfolioImportStatus.PROCESSING,
        });

        logger.info(`Portfolio import processing completed for portfolioId: ${portfolioId}`, {
          traceId,
          portfolioId,
          customerId: customer.id,
          operation: 'processPortfolioImported',
          layer: 'USE_CASE',
          fileKey,
          processedCount,
          totalLines: line - 1,
        });
      })
      .on('error', error => {
        logger.error(`Error processing file from S3: ${fileKey}`, {
          traceId,
          portfolioId,
          error: error.message,
          line,
          operation: 'processPortfolioImported',
          layer: 'USE_CASE',
        });
      });
  }

  async getOriginalFileStream(customerId: string, portfolioId: string): Promise<Readable> {
    const traceId = CorrelationContextService.getTraceId();
    logger.info('Getting original file stream for portfolio import', {
      traceId,
      customerId,
      portfolioId,
      operation: 'getOriginalFileStream',
      layer: 'USE_CASE',
    });

    const fileKey = `${customerId}/${portfolioId}.csv`;

    const fileStream = await this.s3Service.getFileStream(
      this.portfolioImportFilesBucketName,
      fileKey,
    );

    if (!fileStream) {
      logger.error('File not found in S3', {
        traceId,
        customerId,
        portfolioId,
        operation: 'getOriginalFileStream',
        layer: 'USE_CASE',
        fileKey,
      });
      throw new BusinessException(
        'Portfolio-use-case',
        `Portfolio with id ${portfolioId} file not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return fileStream;
  }

  async generateImportErrosFileStream(portfolioId: string): Promise<Readable> {
    const importErrors = await this.portfolioItemImportErrorAdapter.getAll({ portfolioId });

    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'id', title: 'Portfolio Item Id' },
        { id: 'lineNumber', title: 'Line' },
        { id: 'reason', title: 'Reason' },
        { id: 'createdAt', title: 'Created At' },
      ],
    });

    const readableStream = new Readable({
      read() { },
    });

    readableStream.push(csvStringifier.getHeaderString());

    importErrors.forEach(error => {
      readableStream.push(csvStringifier.stringifyRecords([error]));
    });

    readableStream.push(null);

    return readableStream;
  }

  private async countPortfolioImportedLines(
    customerId: string,
    portfolioId: string,
    customerSegment: string,
  ): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();
    logger.info('Counting portfolio imported lines', {
      traceId,
      customerId,
      portfolioId,
      operation: 'countPortfolioImportedLines',
      layer: 'USE_CASE',
    });

    const fileKey = `${customerId}/${portfolioId}.csv`;
    const s3Stream = await this.getOriginalFileStream(customerId, portfolioId);
    if (!s3Stream) {
      return;
    }

    const lineStream = readline.createInterface({
      input: s3Stream,
      crlfDelay: Infinity,
    });

    let totalQuantity = -1;
    for await (const _ of lineStream) {
      totalQuantity++;
    }

    await this.updateImportData({
      id: portfolioId,
      totalQuantity,
    });

    await this.updateExecutionStatus(portfolioId, PortfolioExecutionStatus.QUEUED);

    const messageBody = { fileKey, portfolioId };
    const queueUrl =
      process.env[this.sqsService.getQueueByTypeAndSegment('IMPORT', customerSegment)];
    await this.sqsService.produce(queueUrl, messageBody);
  }

  private HasBrazilianCountryCode(phoneNumber: string): boolean {
    return phoneNumber.startsWith('55');
  }

  /**
   * Applies custom import transformations based on customer preferences
   * @param customerId - Customer ID to retrieve preferences for
   * @param customDataCombined - The custom data object to transform
   * @param portfolioId - Portfolio ID for tracing purposes
   */
  private async applyCustomImportTransformations(
    customerId: string,
    customDataCombined: any,
    portfolioId: string,
  ): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      // Retrieve customer preferences
      const customerPreferences = await this.getCustomerPreferences(customerId);

      const portfolioPrefs = customerPreferences?.portfolio;
      if (!portfolioPrefs || !portfolioPrefs.customImportConfig) {
        logger.info('No portfolio preferences found, skipping transformations', {
          traceId,
          customerId,
          portfolioId,
          operation: 'applyCustomImportTransformations',
          layer: 'USE_CASE',
        });
        return customDataCombined;
      }

      logger.info('Applying customImportConfig transformations', {
        traceId,
        customerId,
        portfolioId,
        operation: 'applyCustomImportTransformations',
        layer: 'USE_CASE',
        hasDelimiter: !!portfolioPrefs.customImportConfig.delimiter,
        hasTaxRules: !!portfolioPrefs.customImportConfig.taxRules,
        hasHeaderMapping: !!portfolioPrefs.customImportConfig.headerMapping,
      });

      // Calculate custom import config tax rules
      const transformedData = await this.calculateCustomImportConfigTaxRules(
        customDataCombined,
        portfolioPrefs.customImportConfig,
        portfolioId,
      );

      if (!transformedData) {
        return customDataCombined;
      }

      logger.info('CustomImportConfig transformations applied successfully', {
        traceId,
        customerId,
        portfolioId,
        operation: 'applyCustomImportTransformations',
        layer: 'USE_CASE',
        data: customDataCombined,
      });

      return transformedData;
    } catch (error) {
      logger.error('Failed to apply custom import transformations', {
        traceId,
        customerId,
        portfolioId,
        operation: 'applyCustomImportTransformations',
        layer: 'USE_CASE',
        error: error.message,
        errorType: error.constructor?.name,
      });

      return customDataCombined;
    }
  }

  /**
   * Calculates penalty amount using integer arithmetic for financial precision
   * @param originalValue - Original debt value
   * @param penaltyFee - Penalty fee as decimal (e.g., 0.0269 for 2.69%)
   * @returns Penalty amount in cents, or 0 if inputs are invalid
   */
  private calculatePenaltyAmount(originalValue: number | null | undefined, penaltyFee: number | null | undefined): number {
    if (originalValue == null || penaltyFee == null) {
      return 0;
    }

    // Convert to integer arithmetic for precision (work in cents)
    const originalValueCents = Math.round(originalValue * 100);

    // Convert decimal rate to integer representation for precise calculations
    // penaltyFee -> multiply by 10000000 to preserve 7 decimal places
    const penaltyFeeInteger = Math.round(penaltyFee * 10000000);

    // Calculate using integer arithmetic: penaltyAmountCents = (penaltyFeeInteger × originalValueCents) / 10000000
    return Math.round((penaltyFeeInteger * originalValueCents) / 10000000);
  }

  /**
   * Calculates daily interest charges using integer arithmetic for financial precision
   * @param originalValue - Original debt value
   * @param dailyFee - Daily fee as decimal (e.g., 0.0003333 for daily rate)
   * @param overdueDays - Number of overdue days
   * @returns Interest charges in cents, or 0 if inputs are invalid
   */
  private calculateInterestCharges(
    originalValue: number | null | undefined,
    dailyFee: number | null | undefined,
    overdueDays: number | null | undefined,
  ): number {
    if (originalValue == null || dailyFee == null || overdueDays == null) {
      return 0;
    }

    // Convert to integer arithmetic for precision (work in cents)
    const originalValueCents = Math.round(originalValue * 100);

    // Convert decimal rate to integer representation for precise calculations
    // dailyFee -> multiply by 10000000 to preserve 7 decimal places
    const dailyFeeInteger = Math.round(dailyFee * 10000000);

    // Calculate using integer arithmetic: interestChargesCents = (dailyFeeInteger × originalValueCents × overdueDays) / 10000000
    return Math.round((dailyFeeInteger * originalValueCents * overdueDays) / 10000000);
  }

  /**
   * Calculates total corrected amount by summing original value, penalty, and interest charges
   * @param originalValue - Original debt value
   * @param penaltyAmountCents - Penalty amount in cents
   * @param interestChargesCents - Interest charges in cents
   * @returns Total amount in cents, or 0 if original value is invalid
   */
  private calculateTotalAmount(
    originalValue: number | null | undefined,
    penaltyAmountCents: number,
    interestChargesCents: number,
  ): number {
    if (originalValue == null) {
      return 0;
    }

    const originalValueCents = Math.round(originalValue * 100);
    return originalValueCents + penaltyAmountCents + interestChargesCents;
  }

  private calculateCustomImportConfigTaxRules(
    customDataCombined: any,
    customImportConfig: CustomImportConfigDto,
    portfolioId: string,
  ): any | null {
    const traceId = CorrelationContextService.getTraceId();
    const transformedData = { ...customDataCombined };

    try {
      // Parse and validate tax rules from customImportConfig
      if (!customImportConfig?.taxRules) {
        logger.info('No tax rules found in custom import config, skipping calculations', {
          traceId,
          portfolioId,
          operation: 'calculateCustomImportConfigTaxRules',
          layer: 'USE_CASE',
        });
        return transformedData;
      }

      const { penaltyFee, dailyFee } = customImportConfig.taxRules;
      let penaltyFeeValue = 0;
      let dailyFeeValue = 0;

      if (penaltyFee) {
        penaltyFeeValue = parseFloat(penaltyFee.replace(',', '.'));
        if (!isNaN(penaltyFeeValue)) {
          transformedData['PERCENTUAL_MULTA'] = penaltyFeeValue;
        }
      }

      if (dailyFee) {
        dailyFeeValue = parseFloat(dailyFee.replace(',', '.'));
        if (!isNaN(dailyFeeValue)) {
          transformedData['PERCENTUAL_JUROS_DIARIOS'] = dailyFeeValue;
        }
      }

      logger.info('Calculating custom import config tax rules', {
        traceId,
        portfolioId,
        operation: 'calculateCustomImportConfigTaxRules',
        layer: 'USE_CASE',
        data: transformedData,
      });

      const originalValue = transformedData['VALOR_ORIGINAL_DA_DIVIDA'];
      const overdueDays = transformedData['DIAS_EM_ATRASO'];

      if (
        originalValue &&
        overdueDays !== undefined
      ) {
        // Parse and validate input values
        const originalValueDecimal = parseFloat(originalValue.toString().replace(',', '.'));
        const overdueDaysInt = parseInt(overdueDays.toString());
        const penaltyFeeDecimal = penaltyFeeValue / 100; // Convert percentage to decimal
        const dailyFeeDecimal = dailyFeeValue; // Already in decimal format

        console.log(`originalValueDecimal: ${originalValueDecimal}, overdueDaysInt: ${overdueDaysInt}, penaltyFeeDecimal: ${penaltyFeeDecimal}, dailyFeeDecimal: ${dailyFeeDecimal}`);

        // Perform calculations using extracted methods
        const penaltyAmountCents = this.calculatePenaltyAmount(originalValueDecimal, penaltyFeeDecimal);
        const interestChargesCents = this.calculateInterestCharges(originalValueDecimal, dailyFeeDecimal, overdueDaysInt);
        const totalAmountCents = this.calculateTotalAmount(originalValueDecimal, penaltyAmountCents, interestChargesCents);

        // Convert back to decimal values for final results
        const penaltyAmount = penaltyAmountCents / 100;
        const interestCharges = interestChargesCents / 100;
        const totalAmount = totalAmountCents / 100;

        // Apply results with proper decimal formatting (2 decimal places)
        transformedData['VALOR_TOTAL_MULTA'] = penaltyAmount.toFixed(2);
        transformedData['VALOR_TOTAL_ENCARGOS'] = interestCharges.toFixed(2);
        transformedData['VALOR_TOTAL_CORRIGIDO'] = totalAmount.toFixed(2);

        logger.info('Custom import config tax rules calculated', {
          traceId,
          portfolioId,
          operation: 'calculateCustomImportConfigTaxRules',
          layer: 'USE_CASE',
          originalValueDecimal,
          overdueDaysInt,
          penaltyFeeDecimal,
          dailyFeeDecimal,
          penaltyAmount: transformedData['VALOR_TOTAL_MULTA'],
          interestCharges: transformedData['VALOR_TOTAL_ENCARGOS'],
          totalAmount: transformedData['VALOR_TOTAL_CORRIGIDO'],
        });
      }

      return transformedData;
    } catch (error) {
      logger.error('Failed to calculate custom import config tax rules', {
        traceId,
        portfolioId,
        operation: 'calculateCustomImportConfigTaxRules',
        layer: 'USE_CASE',
        error: error.message,
        errorType: error.constructor?.name,
      });

      return null;
    }
  }

  async processPortfolioItem(portfolioId: string, line: number, data: any): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info(`Processing portfolio item for portfolio ${portfolioId} line: ${line}`, {
      traceId,
      portfolioId,
      operation: 'processPortfolioItem',
      layer: 'USE_CASE',
      line,
      data,
    });

    try {
      const phoneNumber = data['phone_number'] || data['PHONE_NUMBER'];
      const portfolio = await this.portfolioAdapter.get(portfolioId);

      // Accept Only Brazilian Country Code for now (55)
      if (!this.HasBrazilianCountryCode(phoneNumber)) {
        logger.error('Invalid phone number format', {
          traceId,
          portfolioId,
          operation: 'processPortfolioItem',
          layer: 'USE_CASE',
          line,
          phoneNumber,
        });
        throw new BusinessException(
          'Portfolio-use-case',
          `Phone number ${phoneNumber} does not start with 55`,
          BusinessExceptionStatus.INVALID_INPUT,
        );
      }

      if (!portfolio) {
        logger.error('Portfolio not found', {
          traceId,
          portfolioId,
          operation: 'processPortfolioItem',
          layer: 'USE_CASE',
          line,
        });
        return;
      }

      if (!phoneNumber) {
        logger.error('Phone number not found', {
          traceId,
          portfolioId,
          operation: 'processPortfolioItem',
          layer: 'USE_CASE',
          line,
        });

        await this.portfolioItemImportErrorAdapter.create(
          new PortfolioItemImportErrorEntity(
            uuidv4(),
            portfolioId,
            line,
            'Número de telefone não encontrado.',
            RecordStatus.ACTIVE,
          ),
        );
        return;
      }

      const currentPortfolio = await this.portfolioAdapter.get(portfolio.id);
      const importedPortfolioItem = await this.portfolioItemAdapter.findActiveByPortfolioIdAndLine(
        currentPortfolio.id,
        line,
      );

      if (importedPortfolioItem) {
        logger.info('Portfolio item already imported', {
          traceId,
          portfolioId,
          operation: 'processPortfolioItem',
          layer: 'USE_CASE',
          line,
        });
        return;
      }

      const executingCustomerPortfolios = await this.portfolioAdapter.findAllExecutingByCustomerId(
        portfolio.customerId,
      );
      const executingPortfolioIds = executingCustomerPortfolios.map(ports => ports.id);

      const executingPortfolioItems =
        await this.portfolioItemAdapter.findActiveByPortfolioIdsAndPhoneNumber(
          executingPortfolioIds,
          phoneNumber,
        );

      if (executingPortfolioItems.length > 0) {
        logger.error('Phone number already exists in another portfolio', {
          traceId,
          portfolioId,
          operation: 'processPortfolioItem',
          layer: 'USE_CASE',
          line,
          phoneNumber,
          executingPortfolioIds,
        });

        await this.portfolioItemImportErrorAdapter.create(
          new PortfolioItemImportErrorEntity(
            uuidv4(),
            portfolioId,
            line,
            'Número de telefone já existe em outro portfolio em andamento.',
            RecordStatus.ACTIVE,
          ),
        );
        return;
      }

      const importingCustomerPortfolios = await this.findAllImportingByCustomerId(
        portfolio.customerId,
      );
      const importingCustomerPortfoliosIds = importingCustomerPortfolios.map(ports => ports.id);
      const activeImportingPortfolioItens =
        await this.portfolioItemAdapter.findActiveByPortfolioIdsAndPhoneNumber(
          importingCustomerPortfoliosIds,
          phoneNumber,
        );

      if (activeImportingPortfolioItens && activeImportingPortfolioItens.length > 0) {
        logger.error('Phone number already exists in another portfolio', {
          traceId,
          portfolioId,
          operation: 'processPortfolioItem',
          layer: 'USE_CASE',
          line,
          phoneNumber,
          importingCustomerPortfoliosIds,
        });

        await this.portfolioItemImportErrorAdapter.create(
          new PortfolioItemImportErrorEntity(
            uuidv4(),
            portfolioId,
            line,
            'Número de telefone já existe em outro portfolio em andamento.',
            RecordStatus.ACTIVE,
          ),
        );
        return;
      }

      // UpperCase all keys before creating the portfolio item
      const customDataCombined = Object.keys(data).reduce((acc, key) => {
        acc[key.toUpperCase()] = data[key];
        return acc;
      }, {});

      // Apply custom import transformations based on customer preferences
      const customDataTransformed = await this.applyCustomImportTransformations(
        portfolio.customerId,
        customDataCombined,
        portfolioId,
      );

      logger.info(`Creating portfolio item for portfolio ${portfolioId} line: ${line}`, {
        traceId,
        portfolioId,
        operation: 'processPortfolioItem',
        layer: 'USE_CASE',
        line,
        customData: customDataTransformed,
      });

      const createPortfolioItemDto: PortfolioItemDto = {
        portfolioId,
        phoneNumber,
        customData: customDataTransformed,
        line,
      };

      await this.portfolioItemUseCase.create(createPortfolioItemDto);

      logger.info(`Portfolio ${portfolioId} line: ${line} imported successfully`, {
        traceId,
        portfolioId,
        operation: 'processPortfolioItem',
        layer: 'USE_CASE',
        line,
        customData: customDataTransformed,
      });

      return;
    } catch (error) {
      const portfolioItemErrorId = uuidv4();

      logger.error(`Error processing portfolio item for portfolio ${portfolioId} line: ${line}`, {
        traceId,
        portfolioId,
        operation: 'processPortfolioItem',
        layer: 'USE_CASE',
        line,
        error: error.message,
        errorType: error.constructor?.name,
      });

      const errorMessage =
        error instanceof IntegrationException
          ? error.message
          : 'Erro ao processar item do portfolio';

      await this.portfolioItemImportErrorAdapter.create(
        new PortfolioItemImportErrorEntity(
          portfolioItemErrorId,
          portfolioId,
          line,
          errorMessage,
          RecordStatus.ACTIVE,
        ),
      );

      throw error;
    }
  }

  private async createProcessBatch(
    portfolioId: string,
    batch: Array<{ line: number; data: any; portfolioItem: any }>,
    queueUrl: string,
  ): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();
    try {
      const lines = batch.map(item => item.line).join(', ');
      logger.info(`Sending batch to SQS for lines: ${lines}`, {
        traceId,
        portfolioId,
        operation: 'createProcessBatch',
        layer: 'USE_CASE',
        batchSize: batch.length,
      });

      const sqsEntries = batch.map(item => ({
        Id: `${portfolioId}-${item.line}`,
        MessageBody: JSON.stringify({
          portfolioId,
          line: item.line,
          portfolioItem: item.portfolioItem,
        }),
      }));

      await this.sqsService.produceBatch(queueUrl, sqsEntries);
    } catch (error) {
      logger.error(`Error sending batch to SQS queue: ${queueUrl}`, {
        traceId,
        portfolioId,
        operation: 'createProcessBatch',
        layer: 'USE_CASE',
        error: error.message,
        errorType: error.constructor?.name,
      });
    }
  }

  private async getPortfolioEntity(
    portfolioId: string,
    customerId: string,
  ): Promise<PortfolioEntity> {
    const portfolioEntity = await this.portfolioAdapter
      .getAll({ id: portfolioId, customerId })
      .then(port => {
        return port[0];
      });

    if (!portfolioEntity) {
      throw new BusinessException(
        'Portfolio-use-case',
        `Portfolio with id ${portfolioId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return portfolioEntity;
  }

  private async getPortfolioEntityInternal(portfolioId: string): Promise<PortfolioEntity> {
    const portfolioEntity = await this.portfolioAdapter.get(portfolioId);

    if (!portfolioEntity) {
      throw new BusinessException(
        'Portfolio-use-case',
        `Portfolio with id ${portfolioId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return portfolioEntity;
  }

  private createResponsePortfolioDto(portfolioEntity: PortfolioEntity): ResponsePortfolioDto {
    return plainToClass(ResponsePortfolioDto, portfolioEntity);
  }

  /**
   * Retrieves customer preferences with proper error handling and logging
   * @param customerId - The customer ID to retrieve preferences for
   * @param portfolioId - The portfolio ID for tracing purposes
   * @returns Customer preferences or null if not found/error occurred
   */
  private async getCustomerPreferences(customerId: string): Promise<CustomerPreferencesDto | null> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      logger.info('Retrieving customer preferences for portfolio creation', {
        traceId,
        customerId,
        operation: 'getCustomerPreferences',
        layer: 'USE_CASE',
      });

      const preferences = await this.customerPreferencesUseCase.findById(customerId);

      logger.info('Customer preferences retrieved successfully for portfolio creation', {
        traceId,
        customerId,
        operation: 'getCustomerPreferences',
        layer: 'USE_CASE',
        hasPreferences: !!preferences,
      });

      return preferences;
    } catch (error) {
      // Determine if this is a "not found" error or a system error
      const isNotFoundError =
        error.message?.includes('not found') || error.constructor?.name === 'BusinessException';

      const logLevel = isNotFoundError ? 'info' : 'warn';
      const logMessage = isNotFoundError
        ? 'Customer preferences not found, using defaults for portfolio creation'
        : 'Failed to retrieve customer preferences due to system error, using defaults for portfolio creation';

      logger[logLevel](logMessage, {
        traceId,
        customerId,
        operation: 'getCustomerPreferences',
        layer: 'USE_CASE',
        error: error.message,
        errorType: error.constructor?.name,
        isNotFoundError,
        fallbackStrategy: 'useDefaults',
      });

      return null;
    }
  }

  /**
   * Applies customer preferences to portfolio creation with fallbacks to defaults
   * @param createPortfolioDto - The portfolio DTO to enhance with preferences
   * @param preferences - Customer preferences (can be null)
   * @param portfolioId - Portfolio ID for tracing
   * @returns Enhanced portfolio DTO with preference-based configuration
   */
  private applyCustomerPreferencesToPortfolio(
    createPortfolioDto: PortfolioDto,
    preferences: CustomerPreferencesDto | null,
    portfolioId: string,
  ): PortfolioDto {
    const traceId = CorrelationContextService.getTraceId();

    if (!preferences?.portfolio) {
      logger.info(
        'No customer preferences available, using provided values for portfolio creation',
        {
          traceId,
          portfolioId,
          operation: 'applyCustomerPreferences',
          layer: 'USE_CASE',
        },
      );

      // Validate that workflowId is provided when no preferences are available
      if (!createPortfolioDto.workflowId) {
        throw new BusinessException(
          'PortfolioUseCase',
          'Workflow ID is required when no customer preferences are available',
          BusinessExceptionStatus.INVALID_INPUT,
        );
      }

      return createPortfolioDto;
    }

    const portfolioPrefs = preferences.portfolio;

    // Determine the workflow ID to use (explicit value or preference default)
    const workflowId = createPortfolioDto.workflowId || portfolioPrefs.defaultWorkflowId;

    if (!workflowId) {
      throw new BusinessException(
        'PortfolioUseCase',
        'No workflow ID provided and no default workflow found in customer preferences',
        BusinessExceptionStatus.INVALID_INPUT,
      );
    }

    // Apply preference-based defaults while preserving explicitly provided values
    const enhancedDto = new PortfolioDto(
      createPortfolioDto.name,
      workflowId,
      createPortfolioDto.workExpression,
      createPortfolioDto.communicationChannel,
      createPortfolioDto.idleAfter,
      createPortfolioDto.executeImmediately,
      createPortfolioDto.isDefault,
      createPortfolioDto.timezoneUTC,
      createPortfolioDto.followUpCronExpression,
      createPortfolioDto.followUpQuantity,
      createPortfolioDto.followUpAfter,
      createPortfolioDto.followUpWorkflowId,
    );

    // Set the ID if it exists
    if (createPortfolioDto.id) {
      enhancedDto.id = createPortfolioDto.id;
    }

    logger.info('Applied customer preferences to portfolio creation', {
      traceId,
      portfolioId,
      operation: 'applyCustomerPreferences',
      layer: 'USE_CASE',
      appliedPreferences: {
        defaultWorkflowUsed: !createPortfolioDto.workflowId && portfolioPrefs.defaultWorkflowId,
        workflowId: enhancedDto.workflowId,
        timezoneUTC: portfolioPrefs.timezoneUTC,
        followUpWorkflowId: portfolioPrefs.followUpWorkflowId,
        followUpCronExpression: portfolioPrefs.followUpCronExpression,
        followUpQuantity: portfolioPrefs.followUpQuantity,
        followUpIntervalMinutes: portfolioPrefs.followUpIntervalMinutes,
      },
    });

    return enhancedDto;
  }

  /**
   * Validates that all required portfolio configuration is present after applying preferences
   * @param portfolioDto - The portfolio DTO to validate
   * @param preferences - Customer preferences (can be null)
   * @param portfolioId - Portfolio ID for tracing
   */
  private validatePortfolioConfiguration(
    portfolioDto: PortfolioDto,
    preferences: CustomerPreferencesDto | null,
    portfolioId: string,
  ): void {
    const traceId = CorrelationContextService.getTraceId();

    const validationErrors: string[] = [];

    if (!portfolioDto.workflowId) {
      validationErrors.push('Workflow ID is required');
    }

    if (!portfolioDto.name?.trim()) {
      validationErrors.push('Portfolio name is required');
    }

    if (!portfolioDto.workExpression?.trim()) {
      validationErrors.push('Work expression is required');
    }

    if (!portfolioDto.communicationChannel) {
      validationErrors.push('Communication channel is required');
    }

    if (validationErrors.length > 0) {
      logger.error('Portfolio configuration validation failed', {
        traceId,
        portfolioId,
        operation: 'validatePortfolioConfiguration',
        layer: 'USE_CASE',
        validationErrors,
        hasPreferences: !!preferences?.portfolio,
      });

      throw new BusinessException(
        'PortfolioUseCase',
        `Portfolio configuration validation failed: ${validationErrors.join(', ')}`,
        BusinessExceptionStatus.INVALID_INPUT,
      );
    }

    logger.info('Portfolio configuration validation passed', {
      traceId,
      portfolioId,
      operation: 'validatePortfolioConfiguration',
      layer: 'USE_CASE',
      hasPreferences: !!preferences?.portfolio,
    });
  }

  /**
   * Creates a PortfolioEntity with customer preferences applied
   * @param portfolioDto - Enhanced portfolio DTO
   * @param customerId - Customer ID
   * @param originalFileName - Original file name (optional for non-file portfolios)
   * @param fileUrl - File URL (optional for non-file portfolios)
   * @param processingRateLimit - Processing rate limit
   * @param preferences - Customer preferences (can be null)
   * @returns PortfolioEntity with preferences applied
   */
  private createPortfolioEntityWithPreferences({
    portfolioDto,
    customerId,
    originalFileName,
    fileUrl,
    processingRateLimit,
    preferences,
  }: {
    portfolioDto: PortfolioDto;
    customerId: string;
    originalFileName: string | null;
    fileUrl: string | null;
    processingRateLimit: number;
    preferences: CustomerPreferencesDto | null;
  }): PortfolioEntity {
    const traceId = CorrelationContextService.getTraceId();
    const portfolioPrefs = preferences?.portfolio;

    const followUpWorkflowId =
      portfolioDto.followUpWorkflowId || portfolioPrefs?.followUpWorkflowId;
    const followUpExpression =
      portfolioDto.followUpCronExpression || portfolioPrefs?.followUpCronExpression;
    const followUpAfter = portfolioDto.followUpAfter || portfolioPrefs?.followUpIntervalMinutes;
    const maxFollowUps = portfolioDto.followUpQuantity || portfolioPrefs?.followUpQuantity;
    const timezoneUTC = portfolioDto.timezoneUTC || portfolioPrefs?.timezoneUTC;
    const workExpressionPreference =
      portfolioDto.workExpression || portfolioPrefs?.importCronExpression;

    const portfolio = new PortfolioEntity(
      portfolioDto.id,
      portfolioDto.name,
      customerId,
      portfolioDto.workflowId,
      originalFileName,
      fileUrl,
      workExpressionPreference,
      RecordStatus.ACTIVE,
      portfolioDto.executeImmediately,
      processingRateLimit,
      portfolioDto.idleAfter,
      portfolioDto.communicationChannel,
      portfolioDto.isDefault,
      0,
      PortfolioExecutionStatus.WAITING,
      PortfolioImportStatus.UPLOADED,
      0,
      0,
      0,
      undefined,
      followUpWorkflowId,
      followUpExpression,
      followUpAfter,
      maxFollowUps,
      timezoneUTC,
    );

    logger.info('Portfolio entity created successfully with preferences', {
      traceId,
      customerId,
      operation: 'createPortfolioEntity',
      layer: 'USE_CASE',
      data: portfolio,
    });

    return portfolio;
  }

  async createPortfolio(
    createPortfolioDto: PortfolioDto,
    customerId: string,
  ): Promise<ResponsePortfolioDto> {
    const traceId = CorrelationContextService.getTraceId();

    // Generate portfolio ID early for tracing
    createPortfolioDto.id = uuidv4();
    const portfolioId = createPortfolioDto.id;

    logger.info('Starting portfolio creation without file import', {
      traceId,
      customerId,
      portfolioId,
      portfolioName: createPortfolioDto.name,
      operation: 'createPortfolio',
      layer: 'USE_CASE',
    });

    // Retrieve customer preferences
    const customerPreferences = await this.getCustomerPreferences(customerId);

    // Apply customer preferences to portfolio configuration
    const enhancedPortfolioDto = this.applyCustomerPreferencesToPortfolio(
      createPortfolioDto,
      customerPreferences,
      portfolioId,
    );

    // Validate the final configuration
    this.validatePortfolioConfiguration(enhancedPortfolioDto, customerPreferences, portfolioId);

    const customer = await this.customerAdapter.get(customerId);
    const processingRateLimit = Number(process.env.DEFAULT_PORTFOLIO_PROCESSING_RATE_LIMIT) || 0;

    // Create portfolio entity with preference-enhanced configuration
    logger.info('Creating portfolio entity with preferences applied (no file)', {
      traceId,
      customerId,
      portfolioId,
      operation: 'createPortfolioEntity',
      layer: 'USE_CASE',
      hasPreferences: !!customerPreferences?.portfolio,
    });

    const portfolio = await this.portfolioAdapter.create(
      this.createPortfolioEntityWithPreferences({
        portfolioDto: enhancedPortfolioDto,
        customerId: customer.id,
        originalFileName: null, // No original file name
        fileUrl: null, // No file location
        processingRateLimit,
        preferences: customerPreferences,
      }),
    );

    logger.info('Portfolio entity created successfully with preferences (no file)', {
      traceId,
      customerId,
      portfolioId: portfolio.id,
      portfolioName: portfolio.name,
      workflowId: portfolio.workflowId,
      followUpWorkflowId: portfolio.followUpWorkflowId,
      operation: 'createPortfolioEntity',
      layer: 'USE_CASE',
    });

    logger.info('Portfolio created successfully with preferences applied', {
      traceId,
      customerId,
      portfolioId,
      portfolioName: portfolio.name,
      workflowId: portfolio.workflowId,
      operation: 'createPortfolio',
      layer: 'USE_CASE',
    });

    return this.createResponsePortfolioDto(portfolio);
  }

  async getPortfolioItemSampling(portfolioId: string): Promise<PortfolioItemEntity[]> {
    const {
      data: [itemSucceed],
    } = await this.portfolioItemAdapter.findManyPaginated(
      { where: { portfolioId, currentStatus: PortfolioItemStatus.SUCCEED } },
      {
        page: 1,
        limit: 1,
      },
    );

    const {
      data: [itemInProgress],
    } = await this.portfolioItemAdapter.findManyPaginated(
      { where: { portfolioId, currentStatus: PortfolioItemStatus.IN_PROGRESS } },
      {
        page: 1,
        limit: 1,
      },
    );

    const {
      data: [itemWithInteraction],
    } = await this.portfolioItemAdapter.findManyPaginated(
      {
        where: {
          portfolioId,
          currentStatus: PortfolioItemStatus.IN_PROGRESS,
          lastInteraction: { not: null },
        },
      },
      {
        page: 1,
        limit: 1,
      },
    );

    return [itemSucceed, itemInProgress, itemWithInteraction];
  }

  /**
   * Creates an optimized header mapping function for efficient CSV processing
   * @param headerMapping - Customer preferences header mapping configuration
   * @returns Optimized mapping function with O(1) lookup performance
   */
  private createHeaderMappingFunction(
    headerMapping: Record<string, string> | null,
  ): (input: string) => string {
    if (!headerMapping || Object.keys(headerMapping).length === 0) {
      return (input: string) => input; // Identity function for no mapping
    }

    // Pre-compute case-insensitive lookup map for O(1) performance
    const lookupMap = new Map<string, string>();
    Object.entries(headerMapping).forEach(([key, value]) => {
      lookupMap.set(key.toLowerCase(), value.toUpperCase());
    });

    return (input: string): string => {
      const mapped = lookupMap.get(input.toLowerCase());
      return mapped || input;
    };
  }

  /**
   * Applies header mapping transformations to CSV headers or data keys
   * @param input - Either string[] (headers) or object (data row)
   * @param headerMapping - Customer preferences header mapping
   * @returns Transformed input with mappings applied
   */
  private applyHeaderMapping<T extends string[] | Record<string, any>>(
    input: T,
    headerMapping: Record<string, string> | null,
    additionalHeaders?: string[],
  ): T {
    const traceId = CorrelationContextService.getTraceId();
    const mapFunction = this.createHeaderMappingFunction(headerMapping);

    if (!headerMapping || Object.keys(headerMapping).length === 0) {
      if (Array.isArray(input)) {
        logger.info('No header mapping found, using original headers', {
          traceId,
          operation: 'applyHeaderMapping',
          layer: 'USE_CASE',
          originalHeaderCount: input.length,
        });
      }
      return input;
    }

    if (Array.isArray(input)) {
      // Handle CSV headers (string[])
      logger.info('Applying header mapping transformations to headers', {
        traceId,
        operation: 'applyHeaderMapping',
        layer: 'USE_CASE',
        originalHeaderCount: input.length,
        mappingRulesCount: Object.keys(headerMapping).length,
        headerMapping: Object.keys(headerMapping),
      });

      const mappedHeaders = input.map((originalHeader: string) => {
        const mappedHeader = mapFunction(originalHeader);
        if (mappedHeader !== originalHeader) {
          logger.debug('Header mapped', {
            traceId,
            operation: 'applyHeaderMapping',
            originalHeader,
            mappedHeader,
          });
        }
        return mappedHeader;
      });

      // If post-processing headers are provided, ensure they are in mappedHeaders
      if (additionalHeaders && additionalHeaders.length > 0) {
        mappedHeaders.push(...additionalHeaders);
      }

      logger.info('Header mapping completed', {
        traceId,
        operation: 'applyHeaderMapping',
        layer: 'USE_CASE',
        originalHeaderCount: input.length,
        mappedHeaderCount: mappedHeaders.length,
        transformationsApplied: mappedHeaders.filter((header, index) => header !== input[index])
          .length,
      });

      return mappedHeaders as T;
    } else {
      // Handle data object (Record<string, any>)
      const mappedData: Record<string, any> = {};
      Object.entries(input).forEach(([originalKey, value]) => {
        const mappedKey = mapFunction(originalKey);
        mappedData[mappedKey] = value;

        if (mappedKey !== originalKey) {
          logger.debug('Data property mapped', {
            traceId,
            operation: 'applyHeaderMapping',
            originalKey,
            mappedKey,
            value: typeof value === 'string' ? value.substring(0, 50) : value, // Truncate long strings for logging
          });
        }
      });
      return mappedData as T;
    }
  }
}
