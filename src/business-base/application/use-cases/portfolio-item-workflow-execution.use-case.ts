import { logger } from '@edutalent/commons-sdk';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { PortfolioItemWorkflowExecutionPort } from '@business-base/infrastructure/ports/db/portfolio-item-worflow-execution.port';
import { PortfolioItemWorkflowExecutionEntity } from '@business-base/domain/entities/portfolio-item-workflow-execution.entity';
import { InfraWorkflowPort } from '@business-base/infrastructure/ports/http/workflow.port';
import { InfraMessageHubPort } from '@business-base/infrastructure/ports/http/message-hub.port';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import {
  CommunicationChannel,
  MessageType,
  PortfolioExecutionStatus,
  PortfolioItemStatus,
} from '@common/enums';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';
import { ResponsePortfolioItemDto } from '@business-base/application/dto/out/response-portfolio-item.dto';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { PortfolioItemStatusMappingUseCase } from '@business-base/application/use-cases/portfolio-item-status-mapping.use-case';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { MiddlewareResponseOutputEntity } from '@business-base/domain/entities/middleware-response-output.entity';
import { S3Service } from '@common/s3/s3.service';

@Injectable()
export class PortfolioItemWorkflowExecutionUseCase {
  private readonly directMessageFilesBucketName: string;

  constructor(
    @Inject('PortfolioItemWorkflowExecutionPort')
    private readonly portfolioItemWorkflowExecutionAdapter: PortfolioItemWorkflowExecutionPort,
    @Inject('PortfolioItemCustomDataPort')
    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
    @Inject('MiddlewareResponseOutputPort')
    private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort,
    @Inject('InfraWorkflowPort')
    private readonly workflowAdapter: InfraWorkflowPort,
    @Inject('InfraMessageHubPort')
    private readonly messageHubAdapter: InfraMessageHubPort,
    @Inject(forwardRef(() => PortfolioItemUseCase))
    private readonly portfolioItemUseCase: PortfolioItemUseCase,
    @Inject(forwardRef(() => PortfolioUseCase))
    private readonly portfolioUseCase: PortfolioUseCase,
    @Inject(forwardRef(() => PortfolioItemStatusMappingUseCase))
    private readonly portfolioItemStatusMappingUseCase: PortfolioItemStatusMappingUseCase,
    private readonly s3Service: S3Service,
  ) {
    this.directMessageFilesBucketName = process.env.DIRECT_MESSAGE_FILES_BUCKET;
  }

  async processWorkflowExecution(portfolioId: string, portfolioItemId: string): Promise<void> {
    try {
      const [portfolio, portfolioItem] = await Promise.all([
        this.portfolioUseCase.findByIdInternal(portfolioId),
        this.portfolioItemUseCase.findById(portfolioItemId),
        this.portfolioItemWorkflowExecutionAdapter.getAll({
          portfolioItemId,
        }),
      ]);

      //Check if the workflow can be executed because the portfolio item could be cancelled after the import
      if (!this.canExecuteWorkflow(portfolio.executionStatus, portfolioItem.currentStatus)) {
        logger.info(
          `ProcessWorkflowExecution - portofolio item ${portfolioItemId} cannot be executed because of the its status or of the portfolio status`,
        );
        return;
      }

      const workflowExecutionId = await this.startWorkflowExecution(
        portfolioItem,
        portfolio.workflowId,
      );

      await this.executeExistingWorkflow(
        portfolio.customerId,
        portfolioItem,
        workflowExecutionId,
        portfolio.communicationChannel,
      );
    } catch (error) {
      logger.error(
        `Error processing workflow execution for item: ${portfolioItemId}. Error: ${JSON.stringify(
          error,
        )}`,
      );
      throw new BusinessException(
        'PortfolioItemWorkflowExecutionUseCase',
        'processWorkflowExecution',
        error,
      );
    }
  }

  private async startWorkflowExecution(
    portfolioItem: ResponsePortfolioItemDto,
    workflowId: string,
  ): Promise<string> {
    const portfolioItemId = portfolioItem.id;
    logger.info(`Starting workflow for portfolioItem ${portfolioItemId}`);
    const workflowExecution = await this.workflowAdapter.startWorkflow(workflowId);

    await this.portfolioItemWorkflowExecutionAdapter.create(
      new PortfolioItemWorkflowExecutionEntity(
        uuidv4(),
        portfolioItemId,
        workflowExecution.workflowExecutionId,
      ),
    );

    return workflowExecution.workflowExecutionId;
  }

  private async executeExistingWorkflow(
    customerId: string,
    portfolioItem: ResponsePortfolioItemDto,
    workflowExecutionId: string,
    communicationChannel: CommunicationChannel,
  ): Promise<void> {
    const portfolioItemCustomData = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
      portfolioItem.customDataId,
      portfolioItem.id,
    );

    try {
      logger.info(
        `Workflow sending, for execution id: ${workflowExecutionId} and portfolioItemId: ${
          portfolioItem.id
        }. Custom Data: ${JSON.stringify(portfolioItemCustomData)}`,
      );

      portfolioItemCustomData.customData.DATA_ATUAL = new Date().toISOString();

      const workflowExecutionResponse = await this.workflowAdapter.executeWorkflow({
        lang: 'en',
        messageType: MessageType.TEXT,
        params: portfolioItemCustomData.customData,
        workflowExecutionId,
      });

      logger.info(
        `Workflow response, for execution id: ${workflowExecutionId} and portfolioItemId: ${
          portfolioItem.id
        }
      Response: ${JSON.stringify(workflowExecutionResponse)}`,
      );

      const messageString = Buffer.from(workflowExecutionResponse.output).toString('utf-8');

      const portfolio = await this.portfolioUseCase.findByIdInternal(portfolioItem.portfolioId);

      await this.messageHubAdapter.sendMessage({
        customerId,
        to: portfolioItem.phoneNumber,
        messageType: MessageType.TEXT,
        communicationChannel: communicationChannel,
        message: messageString,
        isFirstMessage: !(
          portfolio.executionStatus === PortfolioExecutionStatus.INBOUND || portfolio.isDefault
        ),
      });

      await this.portfolioItemUseCase.updateLastMessageSentAt(portfolioItem.id);
      await this.portfolioItemUseCase.updateItemCurrentStatus(
        portfolioItem.id,
        PortfolioItemStatus.IN_PROGRESS,
      );
    } catch (error) {
      logger.error(
        `Error finishing workflow execution for item: ${portfolioItem.id}. Error: ${JSON.stringify(
          error,
        )}`,
      );
      throw new BusinessException(
        'PortfolioItemWorkflowExecutionUseCase',
        'executeExistingWorkflow',
        error,
      );
    }
  }

  async executeWorkflowToAnswer(
    portfolioItem: ResponsePortfolioItemDto,
    workflowId: string,
    workflowExecutionId: string,
    message: string,
    messageType: MessageType,
    fileUrl?: string,
  ): Promise<void> {
    try {
      logger.info(`Executing workflow to answer for portfolioItem: ${portfolioItem.id}`);
      const customDataResponse = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
        portfolioItem.customDataId,
        portfolioItem.id,
      );

      customDataResponse.customData.DATA_ATUAL = new Date().toISOString();

      const workflowExecutionResponse = await this.workflowAdapter.executeWorkflow({
        lang: 'en',
        messageType: messageType,
        fileUrl,
        params: { prompt: message, ...customDataResponse.customData },
        workflowExecutionId,
      });

      const portfolio = await this.portfolioUseCase.findByIdInternal(portfolioItem.portfolioId);

      const customerId = portfolio.customerId;
      logger.info(
        `Workflow response, for execution id: ${workflowExecutionId} and portfolioItemId: ${
          portfolioItem.id
        }: ${JSON.stringify(workflowExecutionResponse)}`,
      );

      await this.prepareMessageToSend(
        portfolio.customerId,
        portfolioItem.phoneNumber,
        portfolio.communicationChannel,
        workflowExecutionResponse.outputType,
        workflowExecutionResponse.output,
      );

      await this.portfolioItemUseCase.updateLastMessageSentAt(portfolioItem.id);

      if (workflowExecutionResponse.middlewaresResponse) {
        await this.saveMiddlewareResponseOutput(
          portfolioItem.id,
          workflowExecutionResponse.middlewaresResponse,
        );

        await this.portfolioItemStatusMappingUseCase.updatePortfolioItemStatus(
          workflowId,
          portfolioItem.id,
          customerId,
          workflowExecutionResponse,
        );
      }
    } catch (error) {
      await this.portfolioItemUseCase.updateWaitingBusinessUserResponse(portfolioItem.id, true);

      logger.error(
        `Error in workflow to answer for item: ${portfolioItem.id}. Error: ${JSON.stringify(
          error,
        )}`,
      );

      if (error.status === BusinessExceptionStatus.GENERAL_ERROR) {
        throw new BusinessException(
          'PortfolioItemWorkflowExecutionUseCase',
          'executeWorkflowToAnswer',
          error,
        );
      }
    }
  }

  private async prepareMessageToSend(
    customerId: string,
    to: string,
    channel: CommunicationChannel,
    messageType: MessageType,
    output: Buffer,
  ) {
    let message: string | undefined;
    let fileUrl: string | void;

    if (messageType === MessageType.TEXT) {
      message = Buffer.from(output).toString('utf-8');
      logger.info(`Prepared message to send to ${to}: ${message}`);
    } else if (messageType === MessageType.AUDIO) {
      const fileName = `${to}/${uuidv4()}.mp3`;

      fileUrl = await this.s3Service.uploadFile(
        this.directMessageFilesBucketName,
        fileName,
        Buffer.from(output),
      );
      logger.info(`Prepared audio message to ${to}: ${fileUrl}`);
    }

    await this.messageHubAdapter.sendMessage({
      customerId,
      to,
      messageType,
      communicationChannel: channel,
      message,
      fileUrl,
      isFirstMessage: false,
    });
  }

  private async saveMiddlewareResponseOutput(portfolioItemId: string, data: any): Promise<void> {
    try {
      const portfolioItem = await this.portfolioItemUseCase.findById(portfolioItemId);

      logger.info(`SaveMiddlewareResponseOutput - Portfolio item id: ${portfolioItemId}`);

      if (!portfolioItem.middlewareResponseOutputId) {
        logger.info(
          `SaveMiddlewareResponseOutput - Creating middleware response output for portfolio item: ${portfolioItemId}`,
        );

        const middlewareResponseOutputId = uuidv4();

        await this.middlewareResponseOutputAdapter.create(
          new MiddlewareResponseOutputEntity(middlewareResponseOutputId, portfolioItemId, data),
        );

        await this.portfolioItemUseCase.updateMiddlewareResponseOutputId(
          portfolioItemId,
          middlewareResponseOutputId,
        );
      } else {
        logger.info(
          `SaveMiddlewareResponseOutput - Updating middleware response output for portfolio item: ${portfolioItemId}`,
        );

        await this.middlewareResponseOutputAdapter.update(
          portfolioItem.middlewareResponseOutputId,
          portfolioItemId,
          data,
        );
      }
    } catch (error) {
      logger.error(
        `SaveMiddlewareResponseOutput - Error saving middleware response output for portfolio item: ${portfolioItemId}. Error: ${JSON.stringify(
          error,
        )}`,
      );
    }

    return Promise.resolve();
  }

  private canExecuteWorkflow(
    portfolioExecutionStatus: PortfolioExecutionStatus,
    portfolioItemStatus: PortfolioItemStatus,
  ): boolean {
    return (
      portfolioExecutionStatus !== PortfolioExecutionStatus.PAUSED &&
      portfolioExecutionStatus !== PortfolioExecutionStatus.CANCELLED &&
      portfolioItemStatus !== PortfolioItemStatus.UNLINKED &&
      portfolioItemStatus !== PortfolioItemStatus.CANCELLED &&
      portfolioItemStatus !== PortfolioItemStatus.PAUSED &&
      portfolioItemStatus !== PortfolioItemStatus.SUCCEED &&
      portfolioItemStatus !== PortfolioItemStatus.FAILED &&
      portfolioItemStatus !== PortfolioItemStatus.OPTED_OUT &&
      portfolioItemStatus !== PortfolioItemStatus.FINISHED
    );
  }
}
