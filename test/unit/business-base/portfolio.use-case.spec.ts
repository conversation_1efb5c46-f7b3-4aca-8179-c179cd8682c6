import { Test, TestingModule } from '@nestjs/testing';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import { CustomImportConfigDto, TaxRulesDto } from '@business-base/application/dto/customer-preferences.dto';
import { S3Service } from '@common/s3/s3.service';
import { SQSService } from '@common/sqs/sqs.service';
import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';

describe('PortfolioUseCase - Tax Rules Calculations', () => {
  let useCase: PortfolioUseCase;

  // Mock all dependencies
  const mockPortfolioAdapter = { create: jest.fn(), get: jest.fn() };
  const mockPortfolioItemAdapter = { create: jest.fn(), get: jest.fn() };
  const mockCustomerAdapter = { get: jest.fn() };
  const mockPortfolioItemImportErrorAdapter = { create: jest.fn(), get: jest.fn() };
  const mockS3Service = { uploadFileMultPart: jest.fn(), getObjectStream: jest.fn() };
  const mockSqsService = { getImportItemQueueByCustomer: jest.fn(), sendBatch: jest.fn() };
  const mockCustomerPreferencesUseCase = { getByCustomerId: jest.fn() };
  const mockPortfolioItemUseCase = { create: jest.fn() };
  const mockPortfolioItemCustomDataAdapter = { create: jest.fn(), get: jest.fn() };
  const mockMiddlewareResponseOutputAdapter = { create: jest.fn(), get: jest.fn() };
  const mockInfraWorkflowAdapter = { create: jest.fn(), get: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PortfolioUseCase,
        { provide: 'PortfolioPort', useValue: mockPortfolioAdapter },
        { provide: 'PortfolioItemPort', useValue: mockPortfolioItemAdapter },
        { provide: 'CustomerPort', useValue: mockCustomerAdapter },
        { provide: 'PortfolioItemImportErrorPort', useValue: mockPortfolioItemImportErrorAdapter },
        { provide: S3Service, useValue: mockS3Service },
        { provide: SQSService, useValue: mockSqsService },
        { provide: CustomerPreferencesUseCase, useValue: mockCustomerPreferencesUseCase },
        { provide: PortfolioItemUseCase, useValue: mockPortfolioItemUseCase },
        { provide: 'PortfolioItemCustomDataPort', useValue: mockPortfolioItemCustomDataAdapter },
        { provide: 'MiddlewareResponseOutputPort', useValue: mockMiddlewareResponseOutputAdapter },
        { provide: 'InfraWorkflowPort', useValue: mockInfraWorkflowAdapter },
      ],
    }).compile();

    useCase = module.get<PortfolioUseCase>(PortfolioUseCase);
  });

  describe('calculatePenaltyAmount', () => {
    it('should calculate penalty amount correctly with valid inputs', () => {
      // Test case: R$ 1000.00 with 2.69% penalty fee
      const originalValue = 1000.00;
      const penaltyFee = 0.0269; // 2.69% as decimal

      // Expected: 1000 * 0.0269 = 26.90
      const result = (useCase as any).calculatePenaltyAmount(originalValue, penaltyFee);
      const expectedCents = Math.round(26.90 * 100); // 2690 cents

      expect(result).toBe(expectedCents);
      expect(result / 100).toBeCloseTo(26.90, 2);
    });

    it('should return 0 when originalValue is null', () => {
      const result = (useCase as any).calculatePenaltyAmount(null, 0.0269);
      expect(result).toBe(0);
    });

    it('should return 0 when originalValue is undefined', () => {
      const result = (useCase as any).calculatePenaltyAmount(undefined, 0.0269);
      expect(result).toBe(0);
    });

    it('should return 0 when penaltyFee is null', () => {
      const result = (useCase as any).calculatePenaltyAmount(1000.00, null);
      expect(result).toBe(0);
    });

    it('should return 0 when penaltyFee is undefined', () => {
      const result = (useCase as any).calculatePenaltyAmount(1000.00, undefined);
      expect(result).toBe(0);
    });

    it('should handle edge case with zero penalty fee', () => {
      const result = (useCase as any).calculatePenaltyAmount(1000.00, 0);
      expect(result).toBe(0);
    });

    it('should handle high precision calculations', () => {
      // Test with high precision values
      const originalValue = 1234.56;
      const penaltyFee = 0.123456; // 12.3456%

      const result = (useCase as any).calculatePenaltyAmount(originalValue, penaltyFee);
      const expectedAmount = 1234.56 * 0.123456;

      expect(result / 100).toBeCloseTo(expectedAmount, 2);
    });
  });

  describe('calculateInterestCharges', () => {
    it('should calculate interest charges correctly with valid inputs', () => {
      // Test case: R$ 1000.00 with 0.0003333 daily fee for 30 days
      const originalValue = 1000.00;
      const dailyFee = 0.0003333; // Daily rate
      const overdueDays = 30;

      // Expected: 1000 * 0.0003333 * 30 = 9.999 ≈ 10.00
      const result = (useCase as any).calculateInterestCharges(originalValue, dailyFee, overdueDays);
      const expectedAmount = 1000.00 * 0.0003333 * 30;

      expect(result / 100).toBeCloseTo(expectedAmount, 2);
    });

    it('should return 0 when originalValue is null', () => {
      const result = (useCase as any).calculateInterestCharges(null, 0.0003333, 30);
      expect(result).toBe(0);
    });

    it('should return 0 when dailyFee is null', () => {
      const result = (useCase as any).calculateInterestCharges(1000.00, null, 30);
      expect(result).toBe(0);
    });

    it('should return 0 when overdueDays is null', () => {
      const result = (useCase as any).calculateInterestCharges(1000.00, 0.0003333, null);
      expect(result).toBe(0);
    });

    it('should handle zero overdue days', () => {
      const result = (useCase as any).calculateInterestCharges(1000.00, 0.0003333, 0);
      expect(result).toBe(0);
    });

    it('should handle high overdue days', () => {
      const originalValue = 1000.00;
      const dailyFee = 0.0003333;
      const overdueDays = 365; // 1 year

      const result = (useCase as any).calculateInterestCharges(originalValue, dailyFee, overdueDays);
      const expectedAmount = 1000.00 * 0.0003333 * 365;

      expect(result / 100).toBeCloseTo(expectedAmount, 2);
    });
  });

  describe('calculateTotalAmount', () => {
    it('should calculate total amount correctly with valid inputs', () => {
      const originalValue = 1000.00;
      const penaltyAmountCents = 2690; // R$ 26.90
      const interestChargesCents = 1000; // R$ 10.00

      // Expected: 1000.00 + 26.90 + 10.00 = 1036.90
      const result = (useCase as any).calculateTotalAmount(originalValue, penaltyAmountCents, interestChargesCents);
      const expectedCents = Math.round(1036.90 * 100);

      expect(result).toBe(expectedCents);
      expect(result / 100).toBeCloseTo(1036.90, 2);
    });

    it('should return 0 when originalValue is null', () => {
      const result = (useCase as any).calculateTotalAmount(null, 2690, 1000);
      expect(result).toBe(0);
    });

    it('should return 0 when originalValue is undefined', () => {
      const result = (useCase as any).calculateTotalAmount(undefined, 2690, 1000);
      expect(result).toBe(0);
    });

    it('should handle zero penalty and interest charges', () => {
      const originalValue = 1000.00;
      const result = (useCase as any).calculateTotalAmount(originalValue, 0, 0);
      const expectedCents = Math.round(1000.00 * 100);

      expect(result).toBe(expectedCents);
    });
  });

  describe('calculateCustomImportConfigTaxRules - Integration Tests', () => {
    const portfolioId = 'test-portfolio-id';

    it('should calculate all tax rules correctly with complete valid data', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1000,00',
        DIAS_EM_ATRASO: '30',
        PERCENTUAL_MULTA: 2.69, // 2.69%
        PERCENTUAL_JUROS_DIARIOS: 0.0003333, // Daily rate
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '2,69',
          dailyFee: '0,0003333',
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.PERCENTUAL_MULTA).toBe(2.69);
      expect(result.PERCENTUAL_JUROS_DIARIOS).toBe(0.0003333);

      // Mathematical validation with ±0.01 tolerance
      const expectedPenalty = 1000.00 * (2.69 / 100); // 26.90
      const expectedInterest = 1000.00 * 0.0003333 * 30; // ~9.999
      const expectedTotal = 1000.00 + expectedPenalty + expectedInterest; // ~1036.90

      expect(parseFloat(result.VALOR_TOTAL_MULTA)).toBeCloseTo(expectedPenalty, 2);
      expect(parseFloat(result.VALOR_TOTAL_ENCARGOS)).toBeCloseTo(expectedInterest, 2);
      expect(parseFloat(result.VALOR_TOTAL_CORRIGIDO)).toBeCloseTo(expectedTotal, 2);
    });

    it('should handle missing tax rules gracefully', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1000,00',
        DIAS_EM_ATRASO: '30',
        PERCENTUAL_MULTA: 2.69,
        PERCENTUAL_JUROS_DIARIOS: 0.0003333,
      };

      const customImportConfig: CustomImportConfigDto = {}; // No tax rules

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();

      // Should have only the original value
      expect(result.VALOR_ORIGINAL_DA_DIVIDA).toBe('1000,00');
      expect(result.VALOR_TOTAL_MULTA).toBeUndefined();
      expect(result.VALOR_TOTAL_ENCARGOS).toBeUndefined();
      expect(result.VALOR_TOTAL_CORRIGIDO).toBeUndefined();
    });

    it('should handle missing required calculation fields', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '1000,00',
        // Missing DIAS_EM_ATRASO, PERCENTUAL_MULTA, PERCENTUAL_JUROS_DIARIOS
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '2,69',
          dailyFee: '0,0003333',
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.PERCENTUAL_MULTA).toBe(2.69);
      expect(result.PERCENTUAL_JUROS_DIARIOS).toBe(0.0003333);

      // Should not have calculated values due to missing fields
      expect(result.VALOR_TOTAL_MULTA).toBeUndefined();
      expect(result.VALOR_TOTAL_ENCARGOS).toBeUndefined();
      expect(result.VALOR_TOTAL_CORRIGIDO).toBeUndefined();
    });

    it('should handle edge case with zero values', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '0,00',
        DIAS_EM_ATRASO: '0',
        PERCENTUAL_MULTA: 0,
        PERCENTUAL_JUROS_DIARIOS: 0,
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '0',
          dailyFee: '0',
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.VALOR_TOTAL_MULTA).toBe('0.00');
      expect(result.VALOR_TOTAL_ENCARGOS).toBe('0.00');
      expect(result.VALOR_TOTAL_CORRIGIDO).toBe('0.00');
    });

    it('should handle high debt values correctly', () => {
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: '100000,00', // R$ 100,000.00
        DIAS_EM_ATRASO: '365', // 1 year overdue
        PERCENTUAL_MULTA: 10, // 10%
        PERCENTUAL_JUROS_DIARIOS: 0.001, // 0.1% daily
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: '10',
          dailyFee: '0,001',
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();

      const expectedPenalty = 100000.00 * 0.10; // 10,000.00
      const expectedInterest = 100000.00 * 0.001 * 365; // 36,500.00
      const expectedTotal = 100000.00 + expectedPenalty + expectedInterest; // 146,500.00

      expect(parseFloat(result.VALOR_TOTAL_MULTA)).toBeCloseTo(expectedPenalty, 2);
      expect(parseFloat(result.VALOR_TOTAL_ENCARGOS)).toBeCloseTo(expectedInterest, 2);
      expect(parseFloat(result.VALOR_TOTAL_CORRIGIDO)).toBeCloseTo(expectedTotal, 2);
    });

    it('should handle invalid data gracefully with NaN values', () => {
      // Test with invalid data that will cause parsing to fail
      const customDataCombined = {
        VALOR_ORIGINAL_DA_DIVIDA: 'invalid-number',
        DIAS_EM_ATRASO: 'invalid-number',
        PERCENTUAL_MULTA: 'invalid-number',
        PERCENTUAL_JUROS_DIARIOS: 'invalid-number',
      };

      const customImportConfig: CustomImportConfigDto = {
        taxRules: {
          penaltyFee: 'invalid-number',
          dailyFee: 'invalid-number',
        } as TaxRulesDto,
      };

      const result = (useCase as any).calculateCustomImportConfigTaxRules(
        customDataCombined,
        customImportConfig,
        portfolioId,
      );

      expect(result).toBeDefined();
      expect(result.VALOR_TOTAL_MULTA).toBe('NaN');
      expect(result.VALOR_TOTAL_ENCARGOS).toBe('NaN');
      expect(result.VALOR_TOTAL_CORRIGIDO).toBe('NaN');
    });
  });
});
